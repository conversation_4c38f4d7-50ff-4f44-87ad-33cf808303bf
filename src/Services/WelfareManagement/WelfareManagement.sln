﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35818.85
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WelfareManagement.Application", "WelfareManagement.Application\WelfareManagement.Application.csproj", "{E5938627-74BF-4F12-B580-79B583F8D697}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WelfareManagement.Domain", "WelfareManagement.Domain\WelfareManagement.Domain.csproj", "{2C021052-1A19-44CB-BB25-C5DEF3BC7F52}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WelfareManagement.Infrastructure", "WelfareManagement.Infrastructure\WelfareManagement.Infrastructure.csproj", "{29EA4555-0630-4A8F-A581-BC4E19D28E91}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WelfareManagement.Api", "WelfareManagement.Api\WelfareManagement.Api.csproj", "{C11F277A-C43A-4344-A1F5-61C647BCE288}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WelfareManagement.Contracts", "WelfareManagement.Contracts\WelfareManagement.Contracts.csproj", "{529DBEF2-6B3B-404A-8891-20F07AF6D085}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WelfareManagement.Worker", "WelfareManagement.Worker\WelfareManagement.Worker.csproj", "{A044F524-25A1-4529-9798-862329E98E39}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Database", "Database", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{AD445784-6ACB-4F37-97D0-33E9B42DB0F2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WelfareManagement.UnitTest", "Tests\WelfareManagement.UnitTest\WelfareManagement.UnitTest.csproj", "{CAC3D2C8-FA5E-45F1-9FAA-5A8A92BE48B5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WelfareManagement.IntegrationTest", "Tests\WelfareManagement.IntegrationTest\WelfareManagement.IntegrationTest.csproj", "{78AC7328-8A0D-442F-B204-A9F2D9632FD7}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "WelfareManagement.Database", "Database\WelfareManagement.Database\WelfareManagement.Database.sqlproj", "{7A6C9925-6A82-4D04-890C-752666B27589}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E5938627-74BF-4F12-B580-79B583F8D697}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5938627-74BF-4F12-B580-79B583F8D697}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5938627-74BF-4F12-B580-79B583F8D697}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5938627-74BF-4F12-B580-79B583F8D697}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C021052-1A19-44CB-BB25-C5DEF3BC7F52}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C021052-1A19-44CB-BB25-C5DEF3BC7F52}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C021052-1A19-44CB-BB25-C5DEF3BC7F52}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C021052-1A19-44CB-BB25-C5DEF3BC7F52}.Release|Any CPU.Build.0 = Release|Any CPU
		{29EA4555-0630-4A8F-A581-BC4E19D28E91}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29EA4555-0630-4A8F-A581-BC4E19D28E91}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29EA4555-0630-4A8F-A581-BC4E19D28E91}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29EA4555-0630-4A8F-A581-BC4E19D28E91}.Release|Any CPU.Build.0 = Release|Any CPU
		{C11F277A-C43A-4344-A1F5-61C647BCE288}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C11F277A-C43A-4344-A1F5-61C647BCE288}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C11F277A-C43A-4344-A1F5-61C647BCE288}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C11F277A-C43A-4344-A1F5-61C647BCE288}.Release|Any CPU.Build.0 = Release|Any CPU
		{529DBEF2-6B3B-404A-8891-20F07AF6D085}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{529DBEF2-6B3B-404A-8891-20F07AF6D085}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{529DBEF2-6B3B-404A-8891-20F07AF6D085}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{529DBEF2-6B3B-404A-8891-20F07AF6D085}.Release|Any CPU.Build.0 = Release|Any CPU
		{A044F524-25A1-4529-9798-862329E98E39}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A044F524-25A1-4529-9798-862329E98E39}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A044F524-25A1-4529-9798-862329E98E39}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A044F524-25A1-4529-9798-862329E98E39}.Release|Any CPU.Build.0 = Release|Any CPU
		{CAC3D2C8-FA5E-45F1-9FAA-5A8A92BE48B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CAC3D2C8-FA5E-45F1-9FAA-5A8A92BE48B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CAC3D2C8-FA5E-45F1-9FAA-5A8A92BE48B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CAC3D2C8-FA5E-45F1-9FAA-5A8A92BE48B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{78AC7328-8A0D-442F-B204-A9F2D9632FD7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{78AC7328-8A0D-442F-B204-A9F2D9632FD7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{78AC7328-8A0D-442F-B204-A9F2D9632FD7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{78AC7328-8A0D-442F-B204-A9F2D9632FD7}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A6C9925-6A82-4D04-890C-752666B27589}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A6C9925-6A82-4D04-890C-752666B27589}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A6C9925-6A82-4D04-890C-752666B27589}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{7A6C9925-6A82-4D04-890C-752666B27589}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A6C9925-6A82-4D04-890C-752666B27589}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A6C9925-6A82-4D04-890C-752666B27589}.Release|Any CPU.Deploy.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{CAC3D2C8-FA5E-45F1-9FAA-5A8A92BE48B5} = {AD445784-6ACB-4F37-97D0-33E9B42DB0F2}
		{78AC7328-8A0D-442F-B204-A9F2D9632FD7} = {AD445784-6ACB-4F37-97D0-33E9B42DB0F2}
		{7A6C9925-6A82-4D04-890C-752666B27589} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9D771D42-2C8E-4F3E-BB97-C059EEA8FB41}
	EndGlobalSection
EndGlobal
