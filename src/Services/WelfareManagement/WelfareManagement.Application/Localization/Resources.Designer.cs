﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace S3.MoL.WelfareManagement.Application.Localization {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("S3.MoL.WelfareManagement.Application.Localization.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No data available for the aggregated report of quittance certificates.
        /// </summary>
        public static string AggregatedReportNoData {
            get {
                return ResourceManager.GetString("AggregatedReportNoData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Business Nature with ID {id} does not exist..
        /// </summary>
        public static string BusinessNatureIdNotFound {
            get {
                return ResourceManager.GetString("BusinessNatureIdNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code Type with Id {ID} does not exist..
        /// </summary>
        public static string CodeTypeIDNotExists {
            get {
                return ResourceManager.GetString("CodeTypeIDNotExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code Type is required.
        /// </summary>
        public static string CodeTypeRequired {
            get {
                return ResourceManager.GetString("CodeTypeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No data available for the detailed report of quittance certificates.
        /// </summary>
        public static string DetailedReportNoData {
            get {
                return ResourceManager.GetString("DetailedReportNoData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The duration between the start date and the end date cannot exceed {MaxDurationInDays} days..
        /// </summary>
        public static string DurationExceedsMaxLimit {
            get {
                return ResourceManager.GetString("DurationExceedsMaxLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FromMonth must be between 1 and 12..
        /// </summary>
        public static string FromMonthBetween1And12 {
            get {
                return ResourceManager.GetString("FromMonthBetween1And12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FromYear must be greater than 2000.
        /// </summary>
        public static string FromYearGreaterThan2000 {
            get {
                return ResourceManager.GetString("FromYearGreaterThan2000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FromYear cannot be in the future..
        /// </summary>
        public static string FromYearNotInFuture {
            get {
                return ResourceManager.GetString("FromYearNotInFuture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Governate with ID {id} does not exist..
        /// </summary>
        public static string GovernateIdNotFound {
            get {
                return ResourceManager.GetString("GovernateIdNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Labor Id should be greater than zero.
        /// </summary>
        public static string LaborIdGreaterThanZero {
            get {
                return ResourceManager.GetString("LaborIdGreaterThanZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Labor ID is required.
        /// </summary>
        public static string LaborIdRequired {
            get {
                return ResourceManager.GetString("LaborIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lookup ID must be a numerical value..
        /// </summary>
        public static string LookupIdFormat {
            get {
                return ResourceManager.GetString("LookupIdFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lookup ID is greater than zero..
        /// </summary>
        public static string LookupIdGreaterThanZero {
            get {
                return ResourceManager.GetString("LookupIdGreaterThanZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to lookup ID is required..
        /// </summary>
        public static string LookupIdRequired {
            get {
                return ResourceManager.GetString("LookupIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The requested page number exceeds the maximum allowed page number for search results ({TotalPages} pages)..
        /// </summary>
        public static string max_page_number {
            get {
                return ResourceManager.GetString("max_page_number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing Insurance Sector.
        /// </summary>
        public static string MissingInsuranceSector {
            get {
                return ResourceManager.GetString("MissingInsuranceSector", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no labor registered with this Id..
        /// </summary>
        public static string MissingLabor {
            get {
                return ResourceManager.GetString("MissingLabor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Type Id is not exists.
        /// </summary>
        public static string PartyTypeIDNotExists {
            get {
                return ResourceManager.GetString("PartyTypeIDNotExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DirectorateID must be a positive number..
        /// </summary>
        public static string PositiveDirectorateID {
            get {
                return ResourceManager.GetString("PositiveDirectorateID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FirstPartyID must be a positive number..
        /// </summary>
        public static string PositiveFirstPartyID {
            get {
                return ResourceManager.GetString("PositiveFirstPartyID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SecondPartyID must be a positive number..
        /// </summary>
        public static string PositiveSecondPartyID {
            get {
                return ResourceManager.GetString("PositiveSecondPartyID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StatusID must be a positive number..
        /// </summary>
        public static string PositiveStatusID {
            get {
                return ResourceManager.GetString("PositiveStatusID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TransactionTypeID must be a positive number..
        /// </summary>
        public static string PositiveTransactionTypeID {
            get {
                return ResourceManager.GetString("PositiveTransactionTypeID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At least 5 characters must be entered for the FullName value.
        /// </summary>
        public static string SearchFullNameLength {
            get {
                return ResourceManager.GetString("SearchFullNameLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Page number should be greater than one.
        /// </summary>
        public static string SearchLaborsPageNumberGreaterThanOne {
            get {
                return ResourceManager.GetString("SearchLaborsPageNumberGreaterThanOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At least 5 characters must be entered for the NationalID value.
        /// </summary>
        public static string SearchNIDLength {
            get {
                return ResourceManager.GetString("SearchNIDLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Page size is between {MinimumValue} and {MaximumValue}.
        /// </summary>
        public static string SearchPageSizeLimits {
            get {
                return ResourceManager.GetString("SearchPageSizeLimits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ToMonth must be between 1 and 12..
        /// </summary>
        public static string ToMonthBetween1And12 {
            get {
                return ResourceManager.GetString("ToMonthBetween1And12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ToMonth cannot be less than FromMonth in the same year..
        /// </summary>
        public static string ToMonthGreaterOrEqualToFromMonthInSameYear {
            get {
                return ResourceManager.GetString("ToMonthGreaterOrEqualToFromMonthInSameYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ToYear cannot be less than FromYear..
        /// </summary>
        public static string ToYearGreaterOrEqualToFromYear {
            get {
                return ResourceManager.GetString("ToYearGreaterOrEqualToFromYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ToYear cannot be in the future..
        /// </summary>
        public static string ToYearNotInFuture {
            get {
                return ResourceManager.GetString("ToYearNotInFuture", resourceCulture);
            }
        }
        public static string WelfareTypeIsRequired {
            get {
                return ResourceManager.GetString("WelfareTypeIsRequired", resourceCulture);
            }
        }
        public static string WelfareTypeNotFound {
            get {
                return ResourceManager.GetString("WelfareTypeNotFound", resourceCulture);
            }
        }

        public static string NotBeneficiary {
            get {
                return ResourceManager.GetString("NotBeneficiary", resourceCulture);
            }
        }

        public static string DuplicateRequestInProgress {
            get {
                return ResourceManager.GetString("DuplicateRequestInProgress", resourceCulture);
            }
        }  public static string WelfareTypeIdGreaterThanZero {
            get {
                return ResourceManager.GetString("DuplicateRequestInProgress", resourceCulture);
            }
        }
    }
}
