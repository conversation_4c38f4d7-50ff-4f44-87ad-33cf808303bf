﻿<?xml version="1.0" encoding="utf-8"?>
<root>
    <!-- 
      Microsoft ResX Schema 
      
      Version 2.0
      
      The primary goals of this format is to allow a simple XML format 
      that is mostly human readable. The generation and parsing of the 
      various data types are done through the TypeConverter classes 
      associated with the data types.
      
      Example:
      
      ... ado.net/XML headers & schema ...
      <resheader name="resmimetype">text/microsoft-resx</resheader>
      <resheader name="version">2.0</resheader>
      <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
      <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
      <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
      <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
      <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
          <value>[base64 mime encoded serialized .NET Framework object]</value>
      </data>
      <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
          <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
          <comment>This is a comment</comment>
      </data>
                  
      There are any number of "resheader" rows that contain simple 
      name/value pairs.
      
      Each data row contains a name, and value. The row also contains a 
      type or mimetype. Type corresponds to a .NET class that support 
      text/value conversion through the TypeConverter architecture. 
      Classes that don't support this are serialized and stored with the 
      mimetype set.
      
      The mimetype is used for serialized objects, and tells the 
      ResXResourceReader how to depersist the object. This is currently not 
      extensible. For a given mimetype the value must be set accordingly:
      
      Note - application/x-microsoft.net.object.binary.base64 is the format 
      that the ResXResourceWriter will generate, however the reader can 
      read any of the formats listed below.
      
      mimetype: application/x-microsoft.net.object.binary.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
              : and then encoded with base64 encoding.
      
      mimetype: application/x-microsoft.net.object.soap.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
              : and then encoded with base64 encoding.
  
      mimetype: application/x-microsoft.net.object.bytearray.base64
      value   : The object must be serialized into a byte array 
              : using a System.ComponentModel.TypeConverter
              : and then encoded with base64 encoding.
      -->
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" id="root"
                xmlns="">
        <xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>
        <xsd:element name="root" msdata:IsDataSet="true">
            <xsd:complexType>
                <xsd:choice maxOccurs="unbounded">
                    <xsd:element name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string"/>
                            <xsd:attribute name="type" type="xsd:string"/>
                            <xsd:attribute name="mimetype" type="xsd:string"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="assembly">
                        <xsd:complexType>
                            <xsd:attribute name="alias" type="xsd:string"/>
                            <xsd:attribute name="name" type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1"/>
                            <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3"/>
                            <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="resheader">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>2.0</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="AggregatedReportNoData" xml:space="preserve">
    <value>لا توجد بيانات متاحة للتقرير الاجمالى للمخالصات المميكنة</value>
  </data>
    <data name="CodeTypeIDNotExists" xml:space="preserve">
    <value>Code Type with Id {ID} does not exist.</value>
  </data>
    <data name="CodeTypeRequired" xml:space="preserve">
    <value>يجب إدخال نوع الكود</value>
  </data>
    <data name="DetailedReportNoData" xml:space="preserve">
    <value>لا توجد بيانات متاحة للتقرير التفصيلي للمخالصات المميكنة</value>
  </data>
    <data name="DurationExceedsMaxLimit" xml:space="preserve">
    <value>لا يمكن أن تتجاوز المدة بين تاريخ البداية وتاريخ النهاية {MaxDurationInDays} يوماً.</value>
  </data>
    <data name="FromMonthBetween1And12" xml:space="preserve">
    <value>يجب أن يكون الشهر من بين 1 و 12</value>
  </data>
    <data name="FromYearGreaterThan2000" xml:space="preserve">
    <value>يجب أن تكون السنة من أكبر من 2000.</value>
  </data>
    <data name="FromYearNotInFuture" xml:space="preserve">
    <value>لا يمكن أن تكون السنة من في المستقبل.</value>
  </data>
    <data name="LaborIdGreaterThanZero" xml:space="preserve">
    <value>يجب أن يكون رقم العامل أكبر من الصفر</value>
  </data>
    <data name="LaborIdRequired" xml:space="preserve">
    <value>معرف العامل مطلوب</value>
  </data>
    <data name="max_page_number" xml:space="preserve">
    <value>رقم الصفحة المطلوبة يتجاوز الحد الأقصى المسموح به لرقم الصفحة لنتائج البحث ({TotalPages} صفحة) </value>
  </data>
    <data name="MissingInsuranceSector" xml:space="preserve">
    <value>هذا القطاع التأميني غير موجود بالنظام</value>
  </data>
    <data name="MissingLabor" xml:space="preserve">
    <value>لا يوجد عامل مسجل بهذا المعرف</value>
  </data>
    <data name="PartyIDNotExists" xml:space="preserve">
    <value>معرف نوع الجهة غير موجود</value>
  </data>
    <data name="PositiveDirectorateID" xml:space="preserve">
    <value>يجب أن يكون معرف المديرية رقماً إيجابياً.</value>
  </data>
    <data name="PositiveFirstPartyID" xml:space="preserve">
    <value>يجب أن يكون معرف الطرف الأول اكبر من صفر</value>
  </data>
    <data name="PositiveSecondPartyID" xml:space="preserve">
    <value>يجب أن يكون معرف الطرف الثاني اكبر من صفر</value>
  </data>
    <data name="PositiveStatusID" xml:space="preserve">
    <value>يجب أن يكون معرف الحالة رقماً إيجابياً.</value>
  </data>
    <data name="PositiveTransactionTypeID" xml:space="preserve">
    <value>يجب أن يكون معرف نوع المعاملة رقماً إيجابياً.</value>
  </data>
    <data name="SearchFullNameLength" xml:space="preserve">
    <value>يجب إدخال 5 أحرف على الأقل لقيمة الاسم الكامل</value>
  </data>
    <data name="SearchLaborsPageNumberGreaterThanOne" xml:space="preserve">
    <value>يجب أن يكون رقم الصفحة أكبر من واحد</value>
  </data>
    <data name="SearchNIDLength" xml:space="preserve">
    <value>يجب إدخال 5 أحرف على الأقل لقيمة الهوية الوطنية</value>
  </data>
    <data name="SearchPageSizeLimits" xml:space="preserve">
    <value>حجم الصفحة يتراوح بين {MinimumValue} و{MaximumValue}</value>
  </data>
    <data name="ToMonthBetween1And12" xml:space="preserve">
    <value>يجب أن يكون الشهر إلى بين 1 و 12.</value>
  </data>
    <data name="ToMonthGreaterOrEqualToFromMonthInSameYear" xml:space="preserve">
    <value>لا يمكن أن يكون الشهر إلى أقل من الشهر من في نفس السنة.</value>
  </data>
    <data name="ToYearGreaterOrEqualToFromYear" xml:space="preserve">
    <value>لا يمكن أن تكون السنة إلى أقل من السنة من.</value>
  </data>
    <data name="ToYearNotInFuture" xml:space="preserve">
    <value>لا يمكن أن تكون السنة إلى في المستقبل.</value>
  </data>

    <data name="WelfareTypeIsRequired" xml:space="preserve">
    <value>نوع طلب الرعايه مطلوب</value>
  </data>
    <data name="WelfareTypeNotFound" xml:space="preserve">
  <value>نوع الرعاية غير موجود.</value>
</data>
    <data name="WelfareTypeIdGreaterThanZero" xml:space="preserve">
  <value>يجب أن يكون رقم نوع الرعاية أكبر من صفر</value>
</data>
    <data name="NotBeneficiary" xml:space="preserve">
  <value>العامل غير مسجل كمستفيد.</value>
</data>
    <data name="DuplicateRequestInProgress" xml:space="preserve">
  <value>يوجد طلب رعاية مشابه قيد التنفيذ لهذا العامل.</value>
</data>
</root>