﻿<?xml version="1.0" encoding="utf-8"?>
<root>
    <!-- 
      Microsoft ResX Schema 
      
      Version 2.0
      
      The primary goals of this format is to allow a simple XML format 
      that is mostly human readable. The generation and parsing of the 
      various data types are done through the TypeConverter classes 
      associated with the data types.
      
      Example:
      
      ... ado.net/XML headers & schema ...
      <resheader name="resmimetype">text/microsoft-resx</resheader>
      <resheader name="version">2.0</resheader>
      <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
      <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
      <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
      <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
      <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
          <value>[base64 mime encoded serialized .NET Framework object]</value>
      </data>
      <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
          <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
          <comment>This is a comment</comment>
      </data>
                  
      There are any number of "resheader" rows that contain simple 
      name/value pairs.
      
      Each data row contains a name, and value. The row also contains a 
      type or mimetype. Type corresponds to a .NET class that support 
      text/value conversion through the TypeConverter architecture. 
      Classes that don't support this are serialized and stored with the 
      mimetype set.
      
      The mimetype is used for serialized objects, and tells the 
      ResXResourceReader how to depersist the object. This is currently not 
      extensible. For a given mimetype the value must be set accordingly:
      
      Note - application/x-microsoft.net.object.binary.base64 is the format 
      that the ResXResourceWriter will generate, however the reader can 
      read any of the formats listed below.
      
      mimetype: application/x-microsoft.net.object.binary.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
              : and then encoded with base64 encoding.
      
      mimetype: application/x-microsoft.net.object.soap.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
              : and then encoded with base64 encoding.
  
      mimetype: application/x-microsoft.net.object.bytearray.base64
      value   : The object must be serialized into a byte array 
              : using a System.ComponentModel.TypeConverter
              : and then encoded with base64 encoding.
      -->
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" id="root"
                xmlns="">
        <xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>
        <xsd:element name="root" msdata:IsDataSet="true">
            <xsd:complexType>
                <xsd:choice maxOccurs="unbounded">
                    <xsd:element name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string"/>
                            <xsd:attribute name="type" type="xsd:string"/>
                            <xsd:attribute name="mimetype" type="xsd:string"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="assembly">
                        <xsd:complexType>
                            <xsd:attribute name="alias" type="xsd:string"/>
                            <xsd:attribute name="name" type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1"/>
                            <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3"/>
                            <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="resheader">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>2.0</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="AggregatedReportNoData" xml:space="preserve">
    <value>No data available for the aggregated report of quittance certificates</value>
  </data>
    <data name="BusinessNatureIdNotFound" xml:space="preserve">
    <value>Business Nature with ID {id} does not exist.</value>
  </data>
    <data name="CodeTypeIDNotExists" xml:space="preserve">
    <value>Code Type with Id {ID} does not exist.</value>
  </data>
    <data name="CodeTypeRequired" xml:space="preserve">
    <value>Code Type is required</value>
  </data>
    <data name="DetailedReportNoData" xml:space="preserve">
    <value>No data available for the detailed report of quittance certificates</value>
  </data>
    <data name="DurationExceedsMaxLimit" xml:space="preserve">
    <value>The duration between the start date and the end date cannot exceed {MaxDurationInDays} days.</value>
  </data>
    <data name="FromMonthBetween1And12" xml:space="preserve">
    <value>FromMonth must be between 1 and 12.</value>
  </data>
    <data name="FromYearGreaterThan2000" xml:space="preserve">
    <value>FromYear must be greater than 2000</value>
  </data>
    <data name="FromYearNotInFuture" xml:space="preserve">
    <value>FromYear cannot be in the future.</value>
  </data>
    <data name="GovernateIdNotFound" xml:space="preserve">
    <value>Governate with ID {id} does not exist.</value>
  </data>
    <data name="LaborIdGreaterThanZero" xml:space="preserve">
    <value>Labor Id should be greater than zero</value>
  </data>
    <data name="WelfareTypeIdGreaterThanZero" xml:space="preserve">
    <value>Welfare Type Id should be greater than zero</value>
  </data>
    <data name="LaborIdRequired" xml:space="preserve">
    <value>Labor ID is required</value>
  </data>
    <data name="LookupIdFormat" xml:space="preserve">
    <value>Lookup ID must be a numerical value.</value>
  </data>
    <data name="LookupIdGreaterThanZero" xml:space="preserve">
    <value>Lookup ID is greater than zero.</value>
  </data>
    <data name="LookupIdRequired" xml:space="preserve">
    <value>lookup ID is required.</value>
  </data>
    <data name="max_page_number" xml:space="preserve">
    <value>The requested page number exceeds the maximum allowed page number for search results ({TotalPages} pages).</value>
  </data>
    <data name="MissingInsuranceSector" xml:space="preserve">
    <value>Missing Insurance Sector</value>
  </data>
    <data name="MissingLabor" xml:space="preserve">
    <value>There is no labor registered with this Id.</value>
  </data>
    <data name="PartyTypeIDNotExists" xml:space="preserve">
    <value>Party Type Id is not exists</value>
  </data>
    <data name="PositiveDirectorateID" xml:space="preserve">
    <value>DirectorateID must be a positive number.</value>
  </data>
    <data name="PositiveFirstPartyID" xml:space="preserve">
    <value>FirstPartyID must be a positive number.</value>
  </data>
    <data name="PositiveSecondPartyID" xml:space="preserve">
    <value>SecondPartyID must be a positive number.</value>
  </data>
    <data name="PositiveStatusID" xml:space="preserve">
    <value>StatusID must be a positive number.</value>
  </data>
    <data name="PositiveTransactionTypeID" xml:space="preserve">
    <value>TransactionTypeID must be a positive number.</value>
  </data>
    <data name="SearchFullNameLength" xml:space="preserve">
    <value>At least 5 characters must be entered for the FullName value</value>
  </data>
    <data name="SearchLaborsPageNumberGreaterThanOne" xml:space="preserve">
    <value>Page number should be greater than one</value>
  </data>
    <data name="SearchNIDLength" xml:space="preserve">
    <value>At least 5 characters must be entered for the NationalID value</value>
  </data>
    <data name="SearchPageSizeLimits" xml:space="preserve">
    <value>Page size is between {MinimumValue} and {MaximumValue}</value>
  </data>
    <data name="ToMonthBetween1And12" xml:space="preserve">
    <value>ToMonth must be between 1 and 12.</value>
  </data>
    <data name="ToMonthGreaterOrEqualToFromMonthInSameYear" xml:space="preserve">
    <value>ToMonth cannot be less than FromMonth in the same year.</value>
  </data>
    <data name="ToYearGreaterOrEqualToFromYear" xml:space="preserve">
    <value>ToYear cannot be less than FromYear.</value>
  </data>
    <data name="ToYearNotInFuture" xml:space="preserve">
    <value>ToYear cannot be in the future.</value>
  </data>
    <data name="WelfareTypeIsRequired" xml:space="preserve">
    <value>Welfare Type Is Required</value>
  </data>

    <data name="WelfareTypeNotFound" xml:space="preserve">
  <value>Welfare type not found.</value>
</data>
    <data name="NotBeneficiary" xml:space="preserve">
  <value>Worker is not registered as a beneficiary.</value>
</data>
    <data name="DuplicateRequestInProgress" xml:space="preserve">
  <value>A similar welfare request is already in progress for this worker.</value>
</data>
</root>