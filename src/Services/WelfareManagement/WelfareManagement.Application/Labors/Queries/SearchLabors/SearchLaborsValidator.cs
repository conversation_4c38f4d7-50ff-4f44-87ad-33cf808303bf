﻿using FluentValidation;
using S3.MoL.WelfareManagement.Application.Localization;
using Constants = S3.MoL.WelfareManagement.Application.Support.Constants;

namespace S3.MoL.WelfareManagement.Application.Labors.Queries.SearchLabors
{
    public class SearchLaborsValidator : AbstractValidator<SearchLaborsQuery>
    {
        public SearchLaborsValidator()
        {
            RuleFor(c => c.PageNumber)
                .GreaterThanOrEqualTo(1)
                .WithMessage(Resources.SearchLaborsPageNumberGreaterThanOne);

            RuleFor(c => c.PageSize)
                .Must((code, pageSize, context) =>
                {
                    var valid = pageSize is >= 1 and <= Constants.MaxPageSize;
                    context.MessageFormatter.AppendArgument("MinimumValue", 1);
                    context.MessageFormatter.AppendArgument("MaximumValue", Constants.MaxPageSize);
                    return valid;
                })
                .When(x => Constants.MaxPageSize >= 1)
                .WithMessage(Resources.SearchPageSizeLimits);


            RuleFor(c => c.NationalID)
               .Length(5, Constants.NIDLength)
               .When(c => !string.IsNullOrWhiteSpace(c.NationalID))
               .WithMessage(Resources.SearchNIDLength);


            RuleFor(c => c.FullName)
                 .Length(5, Constants.FullNameLength)
                 .When(c => !string.IsNullOrWhiteSpace(c.FullName))
                 .WithMessage(Resources.SearchFullNameLength);

        }
    }
}
