﻿namespace S3.MoL.WelfareManagement.Application.Labors.Queries.SearchLabors
{
    /// <summary>
    /// Data transfer object (DTO) representing Response for labor information.
    /// </summary>
    public class LaborDto
    {
        /// <summary>
        /// Labor Identification
        /// </summary>
        public long LaborId { get; set; }
        /// <summary>
        /// Labor Full Name
        /// </summary>
        public string FullName { get; set; } = null!;
        /// <summary>
        /// National ID  for Labor Info.
        /// </summary>
        public string NationalId { get; set; } = null!;
        /// <summary>
        /// Last directorate in which the labor works
        /// </summary>
        public int? LastDirectorateId { get; set; }

        /// <summary>
        /// Id of the most recent execution party that employs the labor
        /// </summary>
        public long? LastExecutionPartyId { get; set; }

        /// <summary>
        /// Name of the most recent execution party that employs the labor
        /// </summary>
        public string? LastExecutionPartyName { get; set; }
        /// <summary>
        /// MobileNo  for Labor Info.
        /// </summary>
        public string <PERSON>No { get; set; } = null!;
    }
}
