﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Localization;
using S3.MoL.WelfareManagement.Application.Services.Persistence;

namespace S3.MoL.WelfareManagement.Application.Labors.Queries.SearchLabors
{
    public class SearchLaborsQuery : SearchQueryBase<LaborDto>
    {
        public string? FullName { get; set; }
        public string? NationalID { get; set; }
        public int? LastDirectorateId { get; set; }
        public long? LastExecutionPartyId { get; set; }

        public class SearchLaborsHandler : IRequestHandler<SearchLaborsQuery, Result<SearchResult<LaborDto>>>
        {
            private readonly IWelfareManagementDbContext _WelfareManagementDbContext;

            public SearchLaborsHandler(IWelfareManagementDbContext WelfareManagementDbContext)
            {
                _WelfareManagementDbContext = WelfareManagementDbContext;
            }
            public async Task<Result<SearchResult<LaborDto>>> Handle(SearchLaborsQuery request, CancellationToken cancellationToken)
            {
                var query = CreateQuery(request);
                var data = await query
                   .Paginate(request.PageNumber, request.PageSize)
                   .ToArrayAsync(cancellationToken);
                var metadata = await query.GetResultMetadata(request.PageSize, cancellationToken);


                return metadata.TotalPages != 0 && metadata.TotalPages < request.PageNumber
                   ? Result<SearchResult<LaborDto>>.NotFound<SearchLaborsQuery>(
                       x => x.PageNumber!,
                       Resources.max_page_number.Format(TotalPages => metadata.TotalPages))
                   : Result.Ok(MapResult(data, metadata));
            }


            private IQueryable<LaborDto> CreateQuery(SearchLaborsQuery request)
            {
                var baseQuery = _WelfareManagementDbContext
                    .Labors
                    .Include(x => x.LastExecutionParty)
                    .AsNoTracking()
                    .Select(x => new LaborDto
                    {

                        LaborId = x.LaborId,
                        FullName = x.FullName,
                        NationalId = x.NationalId,
                        LastDirectorateId = x.LastDirectorateId,
                        LastExecutionPartyName = x.LastExecutionParty != null ? x.LastExecutionParty.Name : null,
                        LastExecutionPartyId = x.LastExecutionPartyId,
                        MobileNo = x.MobileNo
                    });

                baseQuery = !request.LastDirectorateId.HasValue
                   ? baseQuery
                   : baseQuery.Where(x => x.LastDirectorateId == request.LastDirectorateId);

                baseQuery = !request.LastExecutionPartyId.HasValue
                  ? baseQuery
                  : baseQuery.Where(x => x.LastExecutionPartyId == request.LastExecutionPartyId);

                baseQuery = string.IsNullOrWhiteSpace(request.FullName)
                    ? baseQuery
                    : baseQuery.Where(x => EF.Functions.Like(x.FullName, request.FullName + "%"));

                baseQuery = string.IsNullOrWhiteSpace(request.NationalID)
                   ? baseQuery
                   : baseQuery.Where(x => x.NationalId == request.NationalID);


                return baseQuery;
            }

            private static SearchResult<LaborDto> MapResult(LaborDto[] data, SearchResultMetadata metadata)
            {
                return new SearchResult<LaborDto>(data, metadata);
            }
        }
    }
}
