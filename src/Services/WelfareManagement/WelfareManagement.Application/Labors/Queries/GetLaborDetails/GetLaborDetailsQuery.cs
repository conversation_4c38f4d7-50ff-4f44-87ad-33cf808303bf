﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Localization;
using S3.MoL.WelfareManagement.Application.Services.Persistence;

namespace S3.MoL.WelfareManagement.Application.Labors.Queries.GetLaborDetails
{
    public class GetLaborDetailsQuery : Request<Result<GetLaborDetailsResult>>
    {
        public long LaborId { get; set; }
        public class GetLaborDetailsQueryHandler : IRequestHandler<GetLaborDetailsQuery, Result<GetLaborDetailsResult>>
        {
            private readonly IWelfareManagementDbContext _WelfareManagementDbContext;
            private GetLaborDetailsQuery _request = null!;
            private CancellationToken _cancellationToken;
            public GetLaborDetailsQueryHandler(IWelfareManagementDbContext WelfareManagementDbContext)
            {
                _WelfareManagementDbContext = WelfareManagementDbContext;
            }
            public async Task<Result<GetLaborDetailsResult>> Handle(GetLaborDetailsQuery request, CancellationToken cancellationToken)
            {
                _request = request;
                _cancellationToken = cancellationToken;

                var labor = await GetExistingLabor();
                if (labor == null)
                {
                    return Result<GetLaborDetailsResult>.NotFound("LaborId", Resources.MissingLabor);
                }

                var laborDetails = MapResult(labor);
                return Result<GetLaborDetailsResult>.Ok(laborDetails);
            }

            private GetLaborDetailsResult MapResult(Labor labor)
            {
                return new GetLaborDetailsResult()
                {
                    LaborId = labor.LaborId,
                    BirthDate = labor.BirthDate,
                    DeathDate = labor.DeathDate,
                    FullName = labor.FullName,
                    GenderId = labor.GenderId,
                    InsuranceNo = labor.InsuranceNo,
                    LastBusinessNatureId = labor.LastBusinessNatureId,
                    LastDirectorateId = labor.LastDirectorateId,
                    MaritalStatusId = labor.MaritalStatusId,
                    MobileNo = labor.MobileNo,
                    NationalId = labor.NationalId,
                    OccupationId = labor.OccupationId,
                    RegistrationDate = labor.RegistrationDate,
                    RegistrationNo = labor.RegistrationNo,
                    LastExecutionPartyName = labor.LastExecutionParty?.Name
                };
            }

            private async Task<Labor?> GetExistingLabor()
            {
                return await _WelfareManagementDbContext.Labors.Include(x => x.LastExecutionParty).AsNoTracking().FirstOrDefaultAsync(x => x.LaborId == _request.LaborId, _cancellationToken);
            }
        }
    }
}
