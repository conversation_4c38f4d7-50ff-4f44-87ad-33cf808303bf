﻿namespace S3.MoL.WelfareManagement.Application.Labors.Queries.GetLaborDetails
{
    public class GetLaborDetailsResult
    {
        /// <summary>
        /// Unique identifier for each labor record
        /// </summary>
        public long LaborId { get; set; }

        /// <summary>
        /// National identification number of the labor
        /// </summary>
        public string NationalId { get; set; } = null!;

        /// <summary>
        /// Full name of the labor
        /// </summary>
        public string FullName { get; set; } = null!;

        /// <summary>
        /// Date of birth of the labor
        /// </summary>
        public DateOnly? BirthDate { get; set; }

        /// <summary>
        /// Identifier of labor gender
        /// </summary>
        public int? GenderId { get; set; }

        /// <summary>
        /// Identifier of labor marital status 
        /// </summary>
        public int? MaritalStatusId { get; set; }

        /// <summary>
        /// Identifier of labor occupation
        /// </summary>
        public int OccupationId { get; set; }

        /// <summary>
        /// Mobile number of the labor
        /// </summary>
        public string MobileNo { get; set; } = null!;

        /// <summary>
        /// Last directorate in which the labor works
        /// </summary>
        public int? LastDirectorateId { get; set; }

        /// <summary>
        /// Last business nature associated with the labor
        /// </summary>
        public int? LastBusinessNatureId { get; set; }


        /// <summary>
        /// Name of the most recent execution party that employs the labor
        /// </summary>
        public string? LastExecutionPartyName { get; set; }

        /// <summary>
        /// Registration number associated with the labor
        /// </summary>
        public string? RegistrationNo { get; set; }

        /// <summary>
        /// Date of registration for the labor
        /// </summary>
        public DateOnly? RegistrationDate { get; set; }

        /// <summary>
        /// Insurance number of the labor
        /// </summary>
        public string? InsuranceNo { get; set; }

        /// <summary>
        /// Date of death of the labor, if applicable
        /// </summary>
        public DateOnly? DeathDate { get; set; }

        /// <summary>
        /// Additional notes or comments related to the labor
        /// </summary>
        public string? Notes { get; set; }

    }
}
