﻿using FluentValidation;
using S3.MoL.WelfareManagement.Application.Localization;

namespace S3.MoL.WelfareManagement.Application.Labors.Queries.GetLaborDetails
{
    public sealed class GetLaborDetailsValidator : AbstractValidator<GetLaborDetailsQuery>
    {
        public GetLaborDetailsValidator()
        {
            RuleFor(x => x.LaborId)
             .NotEmpty()
             .WithMessage(Resources.LaborIdRequired)
             .GreaterThan(0)
             .WithMessage(Resources.LaborIdGreaterThanZero);


        }
    }
}
