﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Services.Persistence;
using LaborDto = S3.MoL.WelfareManagement.Application.Models.LaborDto;

namespace S3.MoL.WelfareManagement.Application.Labors.Commands.SyncLabors
{
    public class Handler : IRequestHandler<SyncLaborCommand, Result<bool>>
    {
        private readonly IWelfareManagementDbContext _WelfareManagementDbContext;

        public Handler(IWelfareManagementDbContext WelfareManagementDbContext)
        {
            _WelfareManagementDbContext = WelfareManagementDbContext;
        }

        public async Task<Result<bool>> Handle(SyncLaborCommand request, CancellationToken cancellationToken)
        {
            var providedLabors = request.Labors;
            var existingLabors = await GetExistingLaborsByNationalID(providedLabors, cancellationToken);

            var laborsToBeAdded = GetLaborsToBeAdded(existingLabors, providedLabors);
            UpdateExistingLabors(existingLabors, providedLabors);

            await SaveChanges(laborsToBeAdded, cancellationToken);
            return Result.Ok(true);
        }

        private async Task<List<Labor>> GetExistingLaborsByNationalID(List<LaborDto> providedLabors, CancellationToken cancellationToken)
        {
            var nationalIds = providedLabors.Select(l => l.NationalID).ToList();

            return await _WelfareManagementDbContext.Labors
                                             .Where(l => nationalIds.Contains(l.NationalId))
                                             .ToListAsync(cancellationToken);
        }

        private static List<Labor> GetLaborsToBeAdded(List<Labor> existingLabors, List<LaborDto> providedLabors)
        {
            return providedLabors
                   .Where(l => !existingLabors.Any(e => e.NationalId == l.NationalID))
                   .Select(MapNewLabor)
                   .ToList();
        }


        private static void UpdateExistingLabors(List<Labor> existingLabors, List<LaborDto> providedLabors)

        {
            var laborsToBeUpdated = existingLabors.Join(providedLabors, el => el.NationalId, pl => pl.NationalID, (existing, provided) => (existing, provided));
            foreach (var labor in laborsToBeUpdated)
            {
                MapUpdatedLabor(labor.provided, labor.existing);
            }
        }

        private static Labor MapNewLabor(LaborDto laborDto) => new()
        {
            LaborId = laborDto.LaborID,
            FullName = laborDto.FullName,
            NationalId = laborDto.NationalID,
            OccupationId = laborDto.OccupationId,
            MobileNo = laborDto.MobileNo,
            LastDirectorateId = laborDto.DirectorateID,
            LastBusinessNatureId = laborDto.BussinessNatureID,
            LastExecutionPartyId = laborDto.LaborID
        };

        private static void MapUpdatedLabor(LaborDto providedLabor, Labor existingLabor)
        {
            existingLabor.OccupationId = providedLabor.OccupationId;
            existingLabor.MobileNo = providedLabor.MobileNo;
            existingLabor.LastDirectorateId = providedLabor.DirectorateID;
            existingLabor.LastBusinessNatureId = providedLabor.BussinessNatureID;
            existingLabor.LastExecutionPartyId = providedLabor.LaborID;
            existingLabor.LastExecutionPartyId = providedLabor.PartyID;
        }

        private async Task SaveChanges(ICollection<Labor> laborsToBeAdded, CancellationToken cancellationToken)
        {
            if (laborsToBeAdded.Count > 0)
            {
                _WelfareManagementDbContext.Labors.AddRange(laborsToBeAdded);
            }

            await _WelfareManagementDbContext.SaveChangesAsync(cancellationToken);
        }
    }
}
