﻿using S3.Core.Application.Exceptions;

namespace S3.MoL.WelfareManagement.Application.Requests
{
    internal static class MapStepConfiguration
    {
        public static WelfareRequestSteps GetDomainRequestStepID(this string stepCode)
        {
            return Enum.TryParse<WelfareRequestSteps>(stepCode, out var stepCodeEnumValue)
                ? stepCodeEnumValue
                : throw new BusinessException($"Not supported step code {stepCode}");
        }
        public static ActionTypes GetDomainRequestActionTypeID(this RequestManagement.Contracts.Enums.RequestActions actionId)
        {
            return actionId switch
            {
                RequestManagement.Contracts.Enums.RequestActions.Create => ActionTypes.Create,
                RequestManagement.Contracts.Enums.RequestActions.Approve => ActionTypes.Approve,
                RequestManagement.Contracts.Enums.RequestActions.Return => ActionTypes.Return,
                RequestManagement.Contracts.Enums.RequestActions.Cancel => ActionTypes.Cancel,
                _ => throw new BusinessException($"Not supported request action {actionId}"),
            };
        }
        public static RequestStatuses GetDomainRequestStatusID(this RequestManagement.Contracts.Enums.RequestStatuses statusId)
        {
            return statusId switch
            {
                RequestManagement.Contracts.Enums.RequestStatuses.InProgress => RequestStatuses.InProgress,
                RequestManagement.Contracts.Enums.RequestStatuses.Completed => RequestStatuses.Completed,
                RequestManagement.Contracts.Enums.RequestStatuses.Cancelled => RequestStatuses.Cancelled,
                _ => throw new BusinessException($"Not supported request status {statusId}"),
            };
        }
        public static ICollection<StepConfiguration> GetDomainStepConfiguration(this RequestManagement.Contracts.Models.CreateNewRequestResponse.CreateWorkFlowStep currentStep)
        {
            return currentStep
                .StepActions
                .Where(x => x.AllowedRoles != null)
                .SelectMany(x => x.AllowedRoles!.Select(role => new { Role = role, x.ActionId }))
                .Where(x => x != null)
                .Select(x => new Domain.Entities.StepConfiguration
                {
                    Role = x.Role,
                    ActionTypeID = x.ActionId.GetDomainRequestActionTypeID(),
                })
                .ToList();
        }
        public static ICollection<StepConfiguration> GetDomainStepConfiguration(this RequestManagement.Contracts.Models.ApplyActionResponse.WorkFlowStep currentStep)
        {
            return currentStep
                .StepActions
                .Where(x => x.AllowedRoles != null)
                .SelectMany(x => x.AllowedRoles!.Select(role => new { Role = role, x.ActionId }))
                .Where(x => x != null)
                .Select(x => new Domain.Entities.StepConfiguration
                {
                    Role = x.Role,
                    ActionTypeID = x.ActionId.GetDomainRequestActionTypeID(),
                })
                .ToList();
        }


        public static RequestManagement.Contracts.Enums.RequestActions GetRequestManagementActionTypeID(this ActionTypes actionId)
        {
            return actionId switch
            {
                ActionTypes.Create => RequestManagement.Contracts.Enums.RequestActions.Create,
                ActionTypes.Approve => RequestManagement.Contracts.Enums.RequestActions.Approve,
                ActionTypes.Return => RequestManagement.Contracts.Enums.RequestActions.Return,
                ActionTypes.Cancel => RequestManagement.Contracts.Enums.RequestActions.Cancel,
                _ => throw new BusinessException($"Not supported request action {actionId}"),
            };
        }
    }
}
