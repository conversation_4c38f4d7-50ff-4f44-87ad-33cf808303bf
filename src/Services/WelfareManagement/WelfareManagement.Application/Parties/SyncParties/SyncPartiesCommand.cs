﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Localization;
using S3.MoL.WelfareManagement.Application.Services.Persistence;


namespace S3.MoL.WelfareManagement.Application.Parties.SyncParties
{
    public sealed class SyncPartiesCommand : Request<Result<bool>>
    {
        public long PartyID { get; set; }

        public int PartyTypeID { get; set; }

        public string Name { get; set; } = null!;

        public sealed class SyncPartiesHandler(IWelfareManagementDbContext _WelfareManagementDbContext) : IRequestHandler<SyncPartiesCommand, Result<bool>>
        {
            private SyncPartiesCommand _request = null!;
            private CancellationToken _cancellationToken;

            public async Task<Result<bool>> Handle(SyncPartiesCommand request, CancellationToken cancellationToken)
            {
                _request = request;
                _cancellationToken = cancellationToken;
                var isPartyTypeExist = await GetPartyType(_request.PartyTypeID);

                if (!isPartyTypeExist)
                {
                    return Result<bool>.NotFound<SyncPartiesCommand>(x => x.PartyID, Resources.PartyTypeIDNotExists);
                }
                var existedParty = await GetParty(_request.PartyID);
                if (existedParty == null)
                {
                    await CreateParty();
                    return Result.Ok(true);

                }
                await UpdateParty(existedParty);
                return Result.Ok(true);
            }

            private async Task UpdateParty(Party existedParty)
            {
                existedParty.PartyId = _request.PartyID;
                existedParty.PartyTypeId = (int)_request.PartyTypeID;
                existedParty.Name = _request.Name;
                _WelfareManagementDbContext.Parties.Update(existedParty);

                await _WelfareManagementDbContext.SaveChangesAsync(_request.Header, _cancellationToken);

            }

            private async Task CreateParty()
            {
                var party = new Party()
                {
                    PartyId = _request.PartyID,
                    PartyTypeId = _request.PartyTypeID,
                    Name = _request.Name,

                };
                _WelfareManagementDbContext.Parties.Add(party);

                await _WelfareManagementDbContext.SaveChangesAsync(_request.Header, _cancellationToken);

            }

            private async Task<bool> GetPartyType(int partyTypeID)
            {
                return await _WelfareManagementDbContext.PartyTypes.AnyAsync(x => x.PartyTypeId == partyTypeID);
            }

            private async Task<Party?> GetParty(long partyID)
            {
                return await _WelfareManagementDbContext.Parties.FirstOrDefaultAsync(x => x.PartyId == partyID);
            }

        }

    }
}
