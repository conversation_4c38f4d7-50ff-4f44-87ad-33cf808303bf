﻿using FluentValidation;
using S3.MoL.WelfareManagement.Application.Localization;

namespace S3.MoL.WelfareManagement.Application.WelfareRequests.Queries.CheckWelfareType;

public sealed class CheckWelfareTypeValidator : AbstractValidator<CheckWelfareTypeQuery>
{
    public CheckWelfareTypeValidator()
    {
        RuleFor(x => x.LaborId)
            .NotEmpty()
            .WithMessage(Resources.LaborIdRequired)
            .GreaterThan(0)
            .WithMessage(Resources.LaborIdGreaterThanZero);

        RuleFor(x => x.WelfareTypeId)
            .NotEmpty()
            .WithMessage(Resources.WelfareTypeIsRequired).GreaterThan(0).WithMessage(Resources.LaborIdGreaterThanZero);
    }
}