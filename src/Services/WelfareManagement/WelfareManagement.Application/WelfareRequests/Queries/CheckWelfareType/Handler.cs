using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Localization;
using S3.MoL.WelfareManagement.Application.Services.Persistence;
using S3.MoL.WelfareManagement.Domain.Entities;
using S3.MoL.WelfareManagement.Domain.Enums;

namespace S3.MoL.WelfareManagement.Application.WelfareRequests.Queries.CheckWelfareType;

/// <summary>
///     Welfare Request Eligibility Checks:
///     [✔] 1. Labor must exist
///     [✔] 2. WelfareType must exist
///     [✔] 3. Labor must be a registered beneficiary
///     [✔] 4. No other in-progress request of the same type allowed
///     [✔] 5. Eligibility based on maximum limit:
///     - General rule for most types
///     - Special rule for Newborns (twins count as 2)
/// </summary>
public class Handler(IWelfareManagementDbContext dbContext, ILogger<Handler> logger)
    : IRequestHandler<CheckWelfareTypeQuery, Result<CheckWelfareTypeResult>>
{
    public async Task<Result<CheckWelfareTypeResult>> Handle(CheckWelfareTypeQuery request,
        CancellationToken cancellationToken)
    {
        // [1] Labor must exist
        var labor = await GetLaborAsync(request.LaborId, cancellationToken);
        if (labor is null)
        {
            logger.LogWarning("Labor ID {LaborId} not found", request.LaborId);
            return Result<CheckWelfareTypeResult>.NotFound(nameof(request.LaborId), Resources.MissingLabor);
        }

        // [2] WelfareType must exist
        var welfareType = await GetWelfareTypeAsync(request.WelfareTypeId, cancellationToken);
        if (welfareType is null)
        {
            logger.LogWarning("WelfareType ID {WelfareTypeId} not found", request.WelfareTypeId);
            return Result<CheckWelfareTypeResult>.NotFound(nameof(request.WelfareTypeId),
                Resources.WelfareTypeNotFound);
        }

        // [3] Labor must be a registered beneficiary
        if (!labor.IsBeneficiary)
        {
            logger.LogInformation("Labor ID {LaborId} is not a beneficiary", request.LaborId);
            return Result<CheckWelfareTypeResult>.Forbidden(nameof(labor), Resources.NotBeneficiary);
        }

        // [4] No in-progress request of the same type allowed
        if (await HasInProgressRequestAsync(request, cancellationToken))
        {
            logger.LogInformation("Duplicate request found for Labor ID {LaborId} and WelfareType {WelfareTypeId}",
                request.LaborId, request.WelfareTypeId);
            return Result<CheckWelfareTypeResult>.Conflict(nameof(WelfareRequest),
                Resources.DuplicateRequestInProgress);
        }

        // [5] Eligibility based on welfare type (general or newborn-specific logic)
        var isEligible = welfareType.WelfareTypeId == (int)WelfareTypes.NewBorn
            ? await CheckNewbornEligibilityAsync(request, welfareType.MaximumLimit, cancellationToken)
            : await CheckGeneralEligibilityAsync(request, welfareType.MaximumLimit, cancellationToken);

        logger.LogInformation("Eligibility check completed for Labor ID {LaborId}. Eligible: {Eligible}",
            request.LaborId,
            isEligible);

        return Result.Ok(new CheckWelfareTypeResult
        {
            IsBeneficairy = isEligible
        });
    }

    private async Task<Labor?> GetLaborAsync(long laborId, CancellationToken ct)
    {
        return await dbContext.Labors
            .AsNoTracking()
            .FirstOrDefaultAsync(l => l.LaborId == laborId, ct);
    }

    private async Task<WelfareType?> GetWelfareTypeAsync(int typeId, CancellationToken ct)
    {
        return await dbContext.WelfareTypes
            .AsNoTracking()
            .FirstOrDefaultAsync(w => w.WelfareTypeId == typeId, ct);
    }

    private async Task<bool> HasInProgressRequestAsync(CheckWelfareTypeQuery request, CancellationToken ct)
    {
        return await dbContext.WelfareRequests
            .AsNoTracking()
            .AnyAsync(r =>
                r.LaborId == request.LaborId &&
                r.WelfareTypeId == request.WelfareTypeId &&
                r.RequestStatusId == (int)RequestStatuses.InProgress, ct);
    }

    /// <summary>
    ///     For most cases, check if completed requests < MaximumLimit
    /// </summary>
    private async Task<bool> CheckGeneralEligibilityAsync(CheckWelfareTypeQuery request, int? maxLimit,
        CancellationToken ct)
    {
        if (maxLimit is null) return true;

        var completedCount = await dbContext.WelfareRequests
            .AsNoTracking()
            .CountAsync(r =>
                r.LaborId == request.LaborId &&
                r.WelfareTypeId == request.WelfareTypeId &&
                r.RequestStatusId == (int)RequestStatuses.Completed, ct);

        return completedCount < maxLimit;
    }

    /// <summary>
    ///     For Newborn case: twins count as 2 benefits
    /// </summary>
    private async Task<bool> CheckNewbornEligibilityAsync(CheckWelfareTypeQuery request, int? maxLimit,
        CancellationToken ct)
    {
        if (maxLimit is null) return true;

        var query = dbContext.SocialWelfareRequests
            .AsNoTracking()
            .Where(s =>
                s.LaborId == request.LaborId &&
                s.WelfareTypeId == request.WelfareTypeId &&
                s.RequestStatusId == (int)RequestStatuses.Completed);

        var total = await query.CountAsync(ct); // Normal births
        var twin = await query.CountAsync(s => s.IsTwin == true, ct); // Twins

        var usedSlots = total + twin;

        if (usedSlots < maxLimit) return true;

        logger.LogInformation("Newborn request limit exceeded. Completed: {Completed}, Twins: {Twins}, Max: {Limit}",
            total, twin, maxLimit);
        return false;
    }
}