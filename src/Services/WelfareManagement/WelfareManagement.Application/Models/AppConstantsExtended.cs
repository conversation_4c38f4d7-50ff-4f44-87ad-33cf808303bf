﻿using S3.Core.Application.Enums;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Localization;

namespace S3.MoL.WelfareManagement.Application.Models
{
    public static partial class AppConstantsExtended
    {
        public static readonly string ShortDateFormat = "dd-MM-yyyy";
        public static readonly string ShortDateFormatSlash = "dd/MM/yyyy";

        public static readonly string BadRequest = ResultStatus.BadRequest.ToString();


        public static readonly Error LookupIdRequired = new(BadRequest, Resources.LookupIdRequired, "LookupId");
        public static readonly Error LookupIdFormat = new(BadRequest, Resources.LookupIdFormat, "LookupId");
        public static readonly Error LookupIdGreaterThanZero = new(BadRequest, Resources.LookupIdGreaterThanZero, "LookupId");
    }
}
