﻿using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Localization;

namespace S3.MoL.WelfareManagement.Application.Models
{
    public static partial class AppUtilityExtended
    {
        public static bool FromCentralAdminUnit(this Core.Application.Models.CQRS.Request request)
        {
            return request.Header?.OrganizationUnitId == 0;
        }

        public static bool IsLookupIdExists<TEnum>(int value) where TEnum : Enum
        {
            return Enum.IsDefined(typeof(TEnum), value);
        }

        public static Result<TValue> CodeTypeIDNotExists<TValue>(long id)
        {
            return Result<TValue>.NotFound("CodeTypeId", Resources.CodeTypeIDNotExists.Format(ID => id));
        }

        public static Error? ValidateLookupId(int? Id)
        {
            return Id == null
                ? AppConstantsExtended.LookupIdRequired
                : !int.TryParse(Id.ToString(), out var lookupId)
                ? AppConstantsExtended.LookupIdFormat
                : lookupId <= 0
                ? AppConstantsExtended.LookupIdGreaterThanZero
                : default;
        }
    }
}
