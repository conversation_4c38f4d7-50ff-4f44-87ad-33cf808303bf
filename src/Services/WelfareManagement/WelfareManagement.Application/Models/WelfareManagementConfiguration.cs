﻿using Microsoft.Extensions.Configuration;
using S3.Core.Application.Exceptions;
using S3.Core.Application.Models.Messaging;
using System.Reflection;

namespace S3.MoL.WelfareManagement.Application.Models
{
    public sealed class WelfareManagementConfiguration
    {
        public bool RunAsWindowsService { get; set; } = false;
        public string ConnectionString { get; set; } = null!;
        public MessagingConfig Messaging { get; set; } = null!;
        public int? CreateCareRequestSheetAtDay { get; set; } = 15;
        public static WelfareManagementConfiguration CreateFrom(IConfiguration configuration, Assembly consumersAssembly, params string[] requiredConsumers)
        {
            var WelfareManagementConfiguration = new WelfareManagementConfiguration();
            configuration
                .GetSection(nameof(WelfareManagementConfiguration))
                .Bind(WelfareManagementConfiguration);

            ConfigurationException.ThrowIfNull(WelfareManagementConfiguration);
            ConfigurationException.ThrowIfNull(WelfareManagementConfiguration.CreateCareRequestSheetAtDay);
            ConfigurationException.ThrowIfNullOrWhiteSpace(WelfareManagementConfiguration.ConnectionString);
            MessagingConfig.Validate(WelfareManagementConfiguration.Messaging, consumersAssembly, requiredConsumers);
            return WelfareManagementConfiguration;
        }
    }
}
