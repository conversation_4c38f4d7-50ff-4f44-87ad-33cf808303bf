﻿using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Models;

namespace S3.MoL.WelfareManagement.Application.Services.Persistence;

public interface IWelfareManagementDbContext : IDbContextBase
{
    DbSet<ActionType> ActionTypes { get; set; }

    DbSet<AttachmentType> AttachmentTypes { get; set; }

    DbSet<BatchRequest> BatchRequests { get; set; }

    DbSet<BatchRequestAction> BatchRequestActions { get; set; }

    DbSet<BatchRequestStep> BatchRequestSteps { get; set; }

    DbSet<Beneficiary> Beneficiaries { get; set; }

    DbSet<BusinessNature> BusinessNatures { get; set; }

    DbSet<Directorate> Directorates { get; set; }

    DbSet<DisabilityWelfareRequest> DisabilityWelfareRequests { get; set; }

    DbSet<Gender> Genders { get; set; }

    DbSet<Labor> Labors { get; set; }

    DbSet<MaritalStatus> MaritalStatuses { get; set; }

    DbSet<Memorandum> Memorandums { get; set; }

    DbSet<NotificationReceiverType> NotificationReceiverTypes { get; set; }

    DbSet<Occupation> Occupations { get; set; }

    DbSet<Party> Parties { get; set; }

    DbSet<PartyType> PartyTypes { get; set; }

    DbSet<RelativeRelationType> RelativeRelationTypes { get; set; }

    DbSet<RelativeRelationship> RelativeRelationships { get; set; }

    DbSet<RequestStatus> RequestStatuses { get; set; }

    DbSet<RequestType> RequestTypes { get; set; }

    DbSet<RequesterRelevance> RequesterRelevances { get; set; }

    DbSet<SocialWelfareRequest> SocialWelfareRequests { get; set; }

    DbSet<WelfareCategory> WelfareCategories { get; set; }

    DbSet<WelfareRequest> WelfareRequests { get; set; }

    DbSet<WelfareRequestAction> WelfareRequestActions { get; set; }

    DbSet<WelfareRequestAttachment> WelfareRequestAttachments { get; set; }

    DbSet<WelfareRequestStep> WelfareRequestSteps { get; set; }

    DbSet<WelfareType> WelfareTypes { get; set; }

    DbSet<WorkflowReason> WorkflowReasons { get; set; }

    DbSet<WelfareRequestStatus> WelfareRequestStatuses { get; set; }

    DbSet<StepConfiguration> StepConfigurations { get; set; }

}