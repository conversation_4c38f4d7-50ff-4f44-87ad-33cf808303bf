﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Models;
using S3.MoL.WelfareManagement.Application.Services.Persistence;

namespace S3.MoL.WelfareManagement.Application.Codes.Commands.SyncCodes
{
    public sealed class SyncWorkFlowCodesCommand : Request<Result<bool>>, ISyncCodesCommand
    {
        public int CodeTypeId { get; set; }
        public long ID { get; set; }
        public string Code { get; set; } = null!;
        public string Text { get; set; } = null!;
        public bool IsActive { get; set; } = true;
        public bool IsDeleted { get; set; } = false;
        public string? Text2 { get; set; }
        public long? ParentCodeId { get; set; }
        public decimal? Amount { get; set; }

        public sealed class SyncWorkFlowCodesHandler : IRequestHandler<SyncWorkFlowCodesCommand, Result<bool>>
        {
            private readonly IWelfareManagementDbContext _WelfareManagementDbContext;
            private SyncWorkFlowCodesCommand _request = null!;
            private CancellationToken _cancellationToken;
            public SyncWorkFlowCodesHandler(IWelfareManagementDbContext WelfareManagementDbContext)
            {
                _WelfareManagementDbContext = WelfareManagementDbContext;
            }
            public async Task<Result<bool>> Handle(SyncWorkFlowCodesCommand request, CancellationToken cancellationToken)
            {
                _request = request;
                _cancellationToken = cancellationToken;

                var isCodeTypeExist = AppUtilityExtended.IsLookupIdExists<CodeTypes>(_request.CodeTypeId);
                if (!isCodeTypeExist)
                {
                    return AppUtilityExtended.CodeTypeIDNotExists<bool>(_request.CodeTypeId);
                }
                await ManageCodeEntity(request.CodeTypeId);
                return Result.Ok(true);
            }

            private async Task ManageCodeEntity(int codeTypeId)
            {
                switch (codeTypeId)
                {
                    case (int)CodeTypes.WelfareManagementWorkflowReasons:
                        await ManageWorkFlowReasonsEntity();
                        break;

                }
                await _WelfareManagementDbContext.SaveChangesAsync(_request.Header, _cancellationToken);
            }

            private async Task ManageWorkFlowReasonsEntity()
            {
                var dbWorkflowReasons = await _WelfareManagementDbContext
              .WorkflowReasons
              .FirstOrDefaultAsync(x => x.WorkflowReasonId == _request.ID, _cancellationToken);

                if (dbWorkflowReasons != null)
                {
                    UpdateWorkflowReasonsEntity(dbWorkflowReasons);
                    return;
                }
                CreateWorkflowReasonsEntity();
            }

            private void CreateWorkflowReasonsEntity()
            {
                if (_request.IsDeleted || !_request.IsActive)
                {
                    return;
                }
                var workflowReason = new WorkflowReason()
                {
                    WorkflowReasonId = (int)_request.ID,
                    Code = _request.Code,
                    Text = _request.Text,
                    Text2 = _request.Text2,
                    IsDeleted = _request.IsDeleted,
                    IsActive = _request.IsActive,
                };
                _WelfareManagementDbContext.WorkflowReasons.Add(workflowReason);

            }

            private void UpdateWorkflowReasonsEntity(WorkflowReason dbWorkflowReasons)
            {
                dbWorkflowReasons.WorkflowReasonId = (int)_request.ID;
                dbWorkflowReasons.Code = _request.Code;
                dbWorkflowReasons.Text = _request.Text;
                dbWorkflowReasons.Text2 = _request.Text2;
                dbWorkflowReasons.IsActive = _request.IsActive;
                dbWorkflowReasons.IsDeleted = _request.IsDeleted;
                _WelfareManagementDbContext.WorkflowReasons.Update(dbWorkflowReasons);
            }

        }

    }
}
