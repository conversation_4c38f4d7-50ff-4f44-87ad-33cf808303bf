﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Models;
using S3.MoL.WelfareManagement.Application.Services.Persistence;

namespace S3.MoL.WelfareManagement.Application.Codes.Commands.SyncCodes
{
    public sealed class SyncWelfareManagementCodesCommand : Request<Result<bool>>, ISyncCodesCommand
    {
        public int CodeTypeId { get; set; }
        public long ID { get; set; }
        public string Code { get; set; } = null!;
        public string Text { get; set; } = null!;
        public bool IsActive { get; set; } = true;
        public bool IsDeleted { get; set; } = false;
        public string? Text2 { get; set; }
        public long? ParentCodeId { get; set; }
        public decimal? Amount { get; set; }

        public sealed class SyncWelfareManagementCodesHandler : IRequestHandler<SyncWelfareManagementCodesCommand, Result<bool>>
        {
            private readonly IWelfareManagementDbContext _WelfareManagementDbContext;
            private SyncWelfareManagementCodesCommand _request = null!;
            private CancellationToken _cancellationToken;
            public SyncWelfareManagementCodesHandler(IWelfareManagementDbContext WelfareManagementDbContext)
            {
                _WelfareManagementDbContext = WelfareManagementDbContext;
            }
            public async Task<Result<bool>> Handle(SyncWelfareManagementCodesCommand request, CancellationToken cancellationToken)
            {
                _request = request;
                _cancellationToken = cancellationToken;

                var isCodeTypeExist = AppUtilityExtended.IsLookupIdExists<CodeTypes>(_request.CodeTypeId);
                if (!isCodeTypeExist)
                {
                    return AppUtilityExtended.CodeTypeIDNotExists<bool>(_request.CodeTypeId);
                }
                await ManageCodeEntity(request.CodeTypeId);
                return Result.Ok(true);
            }

            private async Task ManageCodeEntity(int codeTypeId)
            {
                switch (codeTypeId)
                {
                    //case (int)CodeTypes.InsuranceSector:
                    //    await ManageInsuranceSectorEntity();
                    //    break;
                    case (int)CodeTypes.RelativeRelationType:
                        await ManageRelativeRelationTypeEntity();
                        break;

                        //case (int)CodeTypes.AnnualRewardType:
                        //    await ManageAnnualRewardTypeEntity();
                        //    break;

                }
                await _WelfareManagementDbContext.SaveChangesAsync(_request.Header, _cancellationToken);
            }

            //private async Task ManageAnnualRewardTypeEntity()
            //{
            //    var dbAnnualRewardType = await _WelfareManagementDbContext
            //   .AnnualRewardTypes
            //   .FirstOrDefaultAsync(x => x.AnnualRewardId == _request.ID, _cancellationToken);

            //    if (dbAnnualRewardType != null)
            //    {
            //        UpdateAnnualRewardTypeEntity(dbAnnualRewardType);
            //        return;
            //    }
            //    CreateAnnualRewardTypeEntity();
            //}

            //private void CreateAnnualRewardTypeEntity()
            //{
            //    if (_request.IsDeleted || !_request.IsActive)
            //    {
            //        return;
            //    }
            //    var annualRewardType = new AnnualRewardType()
            //    {
            //        AnnualRewardId = (int)_request.ID,
            //        Code = _request.Code,
            //        Text = _request.Text,
            //        Text2 = _request.Text2,
            //        Amount = _request.Amount ?? 0
            //    };
            //    _WelfareManagementDbContext.AnnualRewardTypes.Add(annualRewardType);
            //}

            //private void UpdateAnnualRewardTypeEntity(AnnualRewardType dbAnnualRewardType)
            //{
            //    if (_request.IsDeleted || !_request.IsActive)
            //    {
            //        _WelfareManagementDbContext.AnnualRewardTypes.Remove(dbAnnualRewardType);
            //    }
            //    else
            //    {
            //        dbAnnualRewardType.AnnualRewardId = (int)_request.ID;
            //        dbAnnualRewardType.Code = _request.Code;
            //        dbAnnualRewardType.Text = _request.Text;
            //        dbAnnualRewardType.Text2 = _request.Text2;
            //        dbAnnualRewardType.Amount = _request.Amount ?? 0;

            //        _WelfareManagementDbContext.AnnualRewardTypes.Update(dbAnnualRewardType);
            //    }
            //}

            private async Task ManageRelativeRelationTypeEntity()
            {
                var dbRelativeRelationType = await _WelfareManagementDbContext
              .RelativeRelationTypes
              .FirstOrDefaultAsync(x => x.RelativeRelationTypeId == _request.ID, _cancellationToken);

                if (dbRelativeRelationType != null)
                {
                    UpdateRelativeRelationTypeEntity(dbRelativeRelationType);
                    return;
                }
                CreateRelativeRelationTypeEntity();
            }

            private void CreateRelativeRelationTypeEntity()
            {
                if (_request.IsDeleted || !_request.IsActive)
                {
                    return;
                }
                var relativeRelationType = new RelativeRelationType()
                {
                    RelativeRelationTypeId = (int)_request.ID,
                    Code = _request.Code,
                    Text = _request.Text,
                    Text2 = _request.Text2,
                };
                _WelfareManagementDbContext.RelativeRelationTypes.Add(relativeRelationType);

            }

            private void UpdateRelativeRelationTypeEntity(RelativeRelationType dbRelativeRelationType)
            {
                if (_request.IsDeleted || !_request.IsActive)
                {
                    _WelfareManagementDbContext.RelativeRelationTypes.Remove(dbRelativeRelationType);
                }
                else
                {
                    dbRelativeRelationType.RelativeRelationTypeId = (int)_request.ID;
                    dbRelativeRelationType.Code = _request.Code;
                    dbRelativeRelationType.Text = _request.Text;
                    dbRelativeRelationType.Text2 = _request.Text2;
                    _WelfareManagementDbContext.RelativeRelationTypes.Update(dbRelativeRelationType);
                }
            }

            //private async Task ManageInsuranceSectorEntity()
            //{
            //    var dbInsuranceSectors = await _WelfareManagementDbContext
            //  .InsuranceSectors
            //  .FirstOrDefaultAsync(x => x.InsuranceSectorId == _request.ID, _cancellationToken);

            //    if (dbInsuranceSectors != null)
            //    {
            //        UpdateInsuranceSectorsEntity(dbInsuranceSectors);
            //        return;
            //    }
            //    CreateInsuranceSectorsEntity();
            //}

            //private void CreateInsuranceSectorsEntity()
            //{
            //    if (_request.IsDeleted || !_request.IsActive)
            //    {
            //        return;
            //    }
            //    var insuranceSector = new InsuranceSector()
            //    {
            //        InsuranceSectorId = (int)_request.ID,
            //        Code = _request.Code,
            //        Text = _request.Text,
            //        Text2 = _request.Text2,
            //    };
            //    _WelfareManagementDbContext.InsuranceSectors.Add(insuranceSector);
            //}

            //private void UpdateInsuranceSectorsEntity(InsuranceSector dbInsuranceSectors)
            //{
            //    dbInsuranceSectors.InsuranceSectorId = (int)_request.ID;
            //    dbInsuranceSectors.Code = _request.Code;
            //    dbInsuranceSectors.Text = _request.Text;
            //    dbInsuranceSectors.Text2 = _request.Text2;
            //    dbInsuranceSectors.IsActive = _request.IsActive;
            //    dbInsuranceSectors.IsDeleted = _request.IsDeleted;
            //    _WelfareManagementDbContext.InsuranceSectors.Update(dbInsuranceSectors);
            //}
        }

    }
}
