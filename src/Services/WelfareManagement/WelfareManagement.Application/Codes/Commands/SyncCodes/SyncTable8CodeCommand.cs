﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Enums;
using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Localization;
using S3.MoL.WelfareManagement.Application.Models;
using S3.MoL.WelfareManagement.Application.Services.Persistence;
using S3.MoL.WelfareManagement.Domain.Entities;
using S3.MoL.WelfareManagement.Domain.Enums;

namespace S3.MoL.WelfareManagement.Application.Codes.Commands.SyncCodes
{
    public sealed class SyncTable8CodeCommand : Request<Result<bool>>
    {
        public int CodeTypeId { get; set; }
        public long ID { get; set; }
        public string Code { get; set; } = null!;
        public string Text { get; set; } = null!;
        public string? Text2 { get; set; }
        public long? ParentCodeId { get; set; }
        public decimal WagesRate { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsDeleted { get; set; } = false;

        public sealed class SyncTable8CodeHandler : IRequestHandler<SyncTable8CodeCommand, Result<bool>>
        {
            private readonly IWelfareManagementDbContext _WelfareManagementDbContext;
            private SyncTable8CodeCommand _request = null!;
            private CancellationToken _cancellationToken;
            public SyncTable8CodeHandler(IWelfareManagementDbContext WelfareManagementDbContext)
            {
                _WelfareManagementDbContext = WelfareManagementDbContext;
            }
            public async Task<Result<bool>> Handle(SyncTable8CodeCommand request, CancellationToken cancellationToken)
            {
                _request = request;
                _cancellationToken = cancellationToken;

                var isCodeTypeExist = AppUtilityExtended.IsLookupIdExists<CodeTypes>(_request.CodeTypeId);
                if (!isCodeTypeExist)
                {
                    return AppUtilityExtended.CodeTypeIDNotExists<bool>(_request.CodeTypeId);
                }

                var result = await ManageCodeEntity(request.CodeTypeId);
                if (result.Status == ResultStatus.Ok)
                {
                    await _WelfareManagementDbContext.SaveChangesAsync(_request.Header, _cancellationToken);
                }
                return result;
            }

            private async Task<Result<bool>> ManageCodeEntity(int codeTypeId)
            {
                switch (codeTypeId)
                {
                    case (int)CodeTypes.BusinessNatures:
                        await ManageBussNatureEntity();
                        break;
                }
                return Result.Ok(true);
            }

            private async Task ManageBussNatureEntity()
            {
                var dbBussNature = await _WelfareManagementDbContext.BusinessNatures
                                                                     .FirstOrDefaultAsync(x => x.BusinessNatureId == _request.ID, _cancellationToken);
                if (dbBussNature != null)
                {
                    UpdateBussNatureEntity(dbBussNature);
                    return;
                }
                CreateBussNatureEntity();
            }
            private void CreateBussNatureEntity()
            {
                var bussNature = new BusinessNature()
                {
                    BusinessNatureId = (int)_request.ID,
                    Code = _request.Code,
                    Text = _request.Text,
                    Text2 = _request.Text2 ?? string.Empty,
                    IsActive = _request.IsActive,
                    IsDeleted = _request.IsDeleted
                };
                _WelfareManagementDbContext.BusinessNatures.Add(bussNature);

            }
            private void UpdateBussNatureEntity(BusinessNature dbBussNature)
            {
                dbBussNature.BusinessNatureId = (int)_request.ID;
                dbBussNature.Code = _request.Code;
                dbBussNature.Text = _request.Text;
                dbBussNature.Text2 = _request.Text2 ?? string.Empty;
                dbBussNature.IsActive = _request.IsActive;
                dbBussNature.IsDeleted = _request.IsDeleted;
                _WelfareManagementDbContext.BusinessNatures.Update(dbBussNature);
            }

            private async Task<Result<bool>> IsParentCodeExist(int codeTypeId, long? fkId)
            {
                var isValidId = AppUtilityExtended.ValidateLookupId((int?)fkId);
                return isValidId != null
                    ? Result<bool>.BadRequest(isValidId.Code, isValidId.Message)
                    : codeTypeId switch
                    {
                        (int)CodeTypes.MainWorkItems => await CheckBussNatureExistence(_WelfareManagementDbContext.BusinessNatures, (int)fkId!, _cancellationToken),
                        _ => Result.Ok(true),
                    };
            }
            private static async Task<Result<bool>> CheckBussNatureExistence(DbSet<BusinessNature> dbSet, int id, CancellationToken cancellationToken)
            {
                var doesEntityExist = await dbSet.AsNoTracking().AnyAsync(x => x.BusinessNatureId == id, cancellationToken);
                return !doesEntityExist ? Result<bool>.NotFound("BusinessNatureId", Resources.BusinessNatureIdNotFound.Format(id => id)) : Result.Ok(true);
            }

        }
    }
}
