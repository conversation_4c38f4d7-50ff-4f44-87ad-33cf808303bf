﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Models;
using S3.MoL.WelfareManagement.Application.Services.Persistence;

namespace S3.MoL.WelfareManagement.Application.Codes.Commands.SyncCodes
{
    public sealed class SyncGenericCodeCommand : Request<Result<bool>>
    {
        public int CodeTypeId { get; set; }
        public long ID { get; set; }
        public string Code { get; set; } = null!;
        public string Text { get; set; } = null!;
        public string? Text2 { get; set; }
        public long? ParentCodeId { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsDeleted { get; set; } = false;
        public string? AccountNo { get; set; }
        public decimal? Rate { get; set; }

        public sealed class SyncGenericCodeHandler : IRequestHandler<SyncGenericCodeCommand, Result<bool>>
        {
            private readonly IWelfareManagementDbContext _WelfareManagementDbContext;
            private SyncGenericCodeCommand _request = null!;
            private CancellationToken _cancellationToken;
            public SyncGenericCodeHandler(IWelfareManagementDbContext WelfareManagementDbContext)
            {
                _WelfareManagementDbContext = WelfareManagementDbContext;
            }
            public async Task<Result<bool>> Handle(SyncGenericCodeCommand request, CancellationToken cancellationToken)
            {
                _request = request;
                _cancellationToken = cancellationToken;

                var isCodeTypeExist = AppUtilityExtended.IsLookupIdExists<CodeTypes>(_request.CodeTypeId);
                if (!isCodeTypeExist)
                {
                    return AppUtilityExtended.CodeTypeIDNotExists<bool>(_request.CodeTypeId);
                }
                await ManageCodeEntity(request.CodeTypeId);
                return Result.Ok(true);
            }

            private async Task ManageCodeEntity(int codeTypeId)
            {
                switch (codeTypeId)
                {
                    case (int)CodeTypes.LaborOccupations:
                        await ManageOccupationEntity();
                        break;
                    case (int)CodeTypes.Gender:
                        await ManageGenderEntity();
                        break;

                    case (int)CodeTypes.MaritalStatus:
                        await ManageMaritalStatus();
                        break;

                    case (int)CodeTypes.EntityTypes:
                        await ManagePartyTypes();
                        break;
                }
                await _WelfareManagementDbContext.SaveChangesAsync(_request.Header, _cancellationToken);
            }

            private async Task ManageMaritalStatus()
            {
                var dbMaritalStatus = await _WelfareManagementDbContext
                .MaritalStatuses
                .FirstOrDefaultAsync(x => x.MaritalStatusId == _request.ID, _cancellationToken);

                if (dbMaritalStatus != null)
                {
                    UpdateMaritalStatusEntity(dbMaritalStatus);
                    return;
                }
                CreateMaritalStatusEntity();
            }



            private async Task ManageOccupationEntity()
            {
                var dbOccupation = await _WelfareManagementDbContext.Occupations
                                                                     .FirstOrDefaultAsync(x => x.OccupationId == _request.ID, _cancellationToken);
                if (dbOccupation != null)
                {
                    UpdateOccupationEntity(dbOccupation);
                    return;
                }
                CreateOccupationEntity();
            }

            private async Task ManagePartyTypes()
            {
                var dbPartyType = await _WelfareManagementDbContext.PartyTypes
                                                                      .FirstOrDefaultAsync(x => x.PartyTypeId == _request.ID, _cancellationToken);
                if (dbPartyType != null)
                {
                    UpdatePartyType(dbPartyType);
                    return;
                }
                CreatePartyType();
            }

            private void CreatePartyType()
            {
                var contractType = new PartyType()
                {
                    PartyTypeId = (int)_request.ID,
                    Code = _request.Code,
                    Text = _request.Text,
                    Text2 = _request.Text2,
                };
                _WelfareManagementDbContext.PartyTypes.Add(contractType);
            }

            private void UpdatePartyType(PartyType dbPartyType)
            {
                dbPartyType.PartyTypeId = (int)_request.ID;
                dbPartyType.Code = _request.Code;
                dbPartyType.Text = _request.Text;
                dbPartyType.Text2 = _request.Text2;
                dbPartyType.IsActive = _request.IsActive;
                dbPartyType.IsDeleted = _request.IsDeleted;
                _WelfareManagementDbContext.PartyTypes.Update(dbPartyType);
            }

            private void CreateMaritalStatusEntity()
            {

                var taxRate = new MaritalStatus()
                {
                    MaritalStatusId = (int)_request.ID,
                    Code = _request.Code,
                    Text = _request.Text,
                    Text2 = _request.Text2,
                };
                _WelfareManagementDbContext.MaritalStatuses.Add(taxRate);
            }

            private void UpdateMaritalStatusEntity(MaritalStatus dbMaritalStatus)
            {
                dbMaritalStatus.MaritalStatusId = (int)_request.ID;
                dbMaritalStatus.Code = _request.Code;
                dbMaritalStatus.Text = _request.Text;
                dbMaritalStatus.Text2 = _request.Text2;
                _WelfareManagementDbContext.MaritalStatuses.Update(dbMaritalStatus);
            }


            private void CreateOccupationEntity()
            {
                var occupation = new Occupation()
                {
                    OccupationId = (int)_request.ID,
                    Code = _request.Code,
                    Text = _request.Text,
                    Text2 = _request.Text2,
                    IsActive = _request.IsActive,
                    IsDeleted = _request.IsDeleted,
                };
                _WelfareManagementDbContext.Occupations.Add(occupation);
            }
            private void UpdateOccupationEntity(Occupation dbOccupation)
            {
                dbOccupation.OccupationId = (int)_request.ID;
                dbOccupation.Code = _request.Code;
                dbOccupation.Text = _request.Text;
                dbOccupation.Text2 = _request.Text2;
                dbOccupation.IsActive = _request.IsActive;
                dbOccupation.IsDeleted = _request.IsDeleted;
                _WelfareManagementDbContext.Occupations.Update(dbOccupation);
            }


            private async Task ManageGenderEntity()
            {
                var dbGender = await _WelfareManagementDbContext
                    .Genders
                    .FirstOrDefaultAsync(x => x.GenderId == _request.ID, _cancellationToken);
                if (dbGender != null)
                {
                    UpdateGenderEntity(dbGender);
                    return;
                }
                CreateGenderEntity();
            }
            private void CreateGenderEntity()
            {
                var gender = new Gender()
                {
                    GenderId = (int)_request.ID,
                    Code = _request.Code,
                    Text = _request.Text,
                    Text2 = _request.Text2,
                    IsActive = _request.IsActive,
                    IsDeleted = _request.IsDeleted,
                };
                _WelfareManagementDbContext.Genders.Add(gender);
            }
            private void UpdateGenderEntity(Gender dbPaymentChannel)
            {
                dbPaymentChannel.GenderId = (int)_request.ID;
                dbPaymentChannel.Code = _request.Code;
                dbPaymentChannel.Text = _request.Text;
                dbPaymentChannel.Text2 = _request.Text2;
                dbPaymentChannel.IsActive = _request.IsActive;
                dbPaymentChannel.IsDeleted = _request.IsDeleted;
                _WelfareManagementDbContext.Genders.Update(dbPaymentChannel);
            }
        }

    }
}
