﻿using FluentValidation;
using S3.MoL.WelfareManagement.Application.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace S3.MoL.WelfareManagement.Application.Codes.Queries.GetWelfareManagementCodes
{
    public sealed class Validator : AbstractValidator<GetWelfareManagementCodesQuery>
    {
        public Validator()
        {
            RuleFor(x => x.CodeType)
                .NotEmpty()
                .WithMessage(Resources.CodeTypeRequired)
                .IsInEnum()
                .WithMessage(Resources.CodeTypeRequired);
        }
    }
}
