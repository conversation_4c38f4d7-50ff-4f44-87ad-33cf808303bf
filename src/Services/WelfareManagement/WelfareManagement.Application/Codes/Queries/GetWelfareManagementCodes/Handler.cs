﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Application.Services.Persistence;

namespace S3.MoL.WelfareManagement.Application.Codes.Queries.GetWelfareManagementCodes
{
    public sealed class Handler : IRequestHandler<GetWelfareManagementCodesQuery, Result<ResponseCodeItem>>
    {
        private readonly IWelfareManagementDbContext _WelfareManagementDbContext;
        private GetWelfareManagementCodesQuery _request = null!;
        private CancellationToken _cancellationToken;
        public Handler(IWelfareManagementDbContext WelfareManagementDbContext)
        {
            _WelfareManagementDbContext = WelfareManagementDbContext;
        }
        public async Task<Result<ResponseCodeItem>> Handle(GetWelfareManagementCodesQuery request, CancellationToken cancellationToken)
        {
            _request = request;
            _cancellationToken = cancellationToken;
            var result = await GetCodes();
            return new Result<ResponseCodeItem>() { Value = result };
        }
        private async Task<ResponseCodeItem> GetCodes()
        {
            return _request.CodeType switch
            {
                CodeTypes.AttachmentTypes => await GetLaborsAttachmentTypes(),

                _ => new ResponseCodeItem(),
            };
        }
        private async Task<ResponseCodeItem> GetLaborsAttachmentTypes()
        {
            var response = new ResponseCodeItem();
            var list = await _WelfareManagementDbContext.AttachmentTypes
                .AsNoTracking()
                .ToListAsync(_cancellationToken);
            response.Codes = MapLaborsAttachmentTypes(list);
            return response;
        }
        private static List<CodeItem> MapLaborsAttachmentTypes(List<AttachmentType> list)
        {
            var result = new List<CodeItem>();
            if (list != null && list.Count > 0)
            {
                foreach (var attachmentType in list)
                {
                    var item = new CodeItem
                    {
                        CodeID = attachmentType.AttachmentTypeId,
                        Value = attachmentType.Code,
                        Text = attachmentType.Text,
                        Text2 = attachmentType.Text2!,
                        MetaData = JsonConvert.SerializeObject(new { attachmentType.MaxFileCount, attachmentType.SizeLimit, attachmentType.Mandatory, attachmentType.MimeTypes }),
                    };
                    result.Add(item);

                }
            }
            return result;
        }

    }
}
