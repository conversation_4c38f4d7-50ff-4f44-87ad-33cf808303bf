﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace S3.MoL.WelfareManagement.Application.Codes.Queries.GetWelfareManagementCodes
{
    public sealed class CodeItem
    {
        public int CodeID { get; set; }
        public string Value { get; set; } = null!;
        public string Text { get; set; } = null!;
        public string Text2 { get; set; } = null!;
        public string? ParentCodeText { get; set; } = null!;
        public int? ParentCodeId { get; set; } = null!;
        public string? ParentCodeValue { get; set; } = null!;
        public string? MetaData { get; set; } = null!;
        public bool? IsDeleted { get; set; }
        public bool? IsActive { get; set; }
    }
}
