﻿using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Application.Submissions.Commands.CreateExamSubmission;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.Submissions.Commands.CreateExamSubmission
{
    internal class HandlerTest
    {
        private ExamManagementDbContext _context = default!;
        private Mock<IAuditService> _mockAuditService = default!;
        private Mock<ILogger<Handler>> _logger = default!;
        private Handler _handler = default!;

        [SetUp]
        public async Task SetUp()
        {
            _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
            await SeedDBContext(_context);
            _mockAuditService = new Mock<IAuditService>();
            _logger = new Mock<ILogger<Handler>>();
            _handler = new Handler(_context, _mockAuditService.Object, _logger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _context.Dispose();
        }

        [Test]
        public async Task CreateExamSubmission_ShouldCreateSuccessfully()
        {
            var command = new CreateExamSubmissionCommand
            {
                ExaminerId = 1,
                ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889a"),
                Questions = new List<SubmissionAnswerItem>
                {
                    new SubmissionAnswerItem{AnswerIds=[1],QuestionId=1},
                    new SubmissionAnswerItem{AnswerIds=[],QuestionId=2},
                    new SubmissionAnswerItem{AnswerIds=[],QuestionId=3},
                }
            };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
        }

        private static async Task SeedDBContext(IExamManagementDbContext dataContext)
        {
            await dataContext.Submissions.AddAsync(new Submission
            {
                ExaminerId = 1,
                ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f")
            });
            await dataContext.ExamVersions.AddAsync(new ExamVersion
            {
                Id = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
                VersionNumber = "1",
                CreatedDate = DateTime.Now,
                CreatedByUserId = "a"
            });
            await dataContext.ExamSections.AddAsync(new ExamSection
            {
                Id = 1,
                HeaderTitle = "a",
                ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f")
            });
            await dataContext.Questions.AddAsync(new Question
            {
                Id = 1,
                Title = "Test",
                ExamSectionId = 1,
                CreatedDate = DateTime.Now,
                CreatedByUserId = "a"
            });
            await dataContext.Questions.AddAsync(new Question
            {
                Id = 2,
                Title = "Test",
                ExamSectionId = 1,
                CreatedDate = DateTime.Now,
                CreatedByUserId = "a"
            });
            await dataContext.Answers.AddAsync(new Answer
            {
                Id = 1,
                QuestionId = 1,
                IsCorrect = true,
            });

            await dataContext.SaveChangesAsync();
        }

    }
}