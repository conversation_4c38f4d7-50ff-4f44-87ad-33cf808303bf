﻿using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Application.Submissions.Queries.GetExamVersionResult;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Domain.Enums;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.Submissions.Queries.GetExamVersionResult;

internal class HandlerTest
{
    private ExamManagementDbContext _context = default!;
    private GetExamVersionResultHandler _handler = default!;
    private Mock<ILogger<GetExamVersionResultHandler>> _logger = default!;
    private Mock<IAuditService> _mockAuditService = default!;
    private Mock<IFileService> _mockFileService = default!;

    [SetUp]
    public async Task SetUp()
    {
        _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
        await SeedDbContext(_context);

        _mockAuditService = new Mock<IAuditService>();
        _mockFileService = new Mock<IFileService>();
        _logger = new Mock<ILogger<GetExamVersionResultHandler>>();

        _handler = new GetExamVersionResultHandler(
            _context, _mockAuditService.Object, _mockFileService.Object, _logger.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _context.Dispose();
    }

    [Test]
    public async Task GetExamVersionResult_ShouldReturnExamResult_WhenSubmissionExists()
    {
        // Arrange

        var query = new GetExamVersionResultQuery { SubmissionId = 1 };

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
            Assert.That(result.Value, Is.Not.Null);
        });

    }

    [Test]
    public async Task GetExamVersionResult_ShouldReturnNotFound_WhenSubmissionDoesNotExist()
    {
        const int submissionId = 2;
        // Arrange
        var query = new GetExamVersionResultQuery { SubmissionId = submissionId };

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound));
            //Assert.That(result.ErrorMessage, Is.EqualTo(Resources.SubmissionNotFound));
            Assert.That(result.Value, Is.Null);
        });
    }


    private static async Task SeedDbContext(IExamManagementDbContext dataContext)
    {
        var submission = new Submission
        {
            Id = 1,
            ExaminerId = 1,
            ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
            ScorePercentage = 85,
            ExamVersion = new ExamVersion
            {
                Id = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
                VersionNumber = "1.0",
                TotalMarks = 100,
                Status = ExamVersionStatus.Active,
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "admin",
                ExamVersionResultFormats = new List<ExamVersionResultFormat>
                {
                    new()
                    {
                        ResultFormat = new ResultFormat {Id=1, NameEn = "grade", NameAr = "" },

                        GradeRanges = new List<GradeRange>
                        {
                            new() { MinMark = 80, MaxMark = 100, Grade = "A" }
                        }
                    },
                    new()
                    {
                        ResultFormat = new ResultFormat {Id=2, NameEn = "percentage", NameAr = "" }
                    }
                },
                Exam = new()
                {
                    Id = Guid.CreateVersion7(),
                    PassPercentage = 0.5m,
                    Name = "test1",
                    CreatedDate = DateTime.UtcNow,
                    CreatedByUserId = "hany"
                }
            }
        };

        await dataContext.Submissions.AddAsync(submission);
        await dataContext.SaveChangesAsync();
    }
}