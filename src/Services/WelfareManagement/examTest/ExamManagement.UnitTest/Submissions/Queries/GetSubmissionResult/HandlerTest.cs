﻿using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Application.Submissions.Queries.GetSubmissionReview;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.Submissions.Queries.GetSubmissionResult;

internal class HandlerTest
{
    private ExamManagementDbContext _context = null!;
    private Handler _handler = null!;
    private Mock<ILogger<Handler>> _logger = null!;
    private Mock<IFileService> _mockFileService = null!;


    [SetUp]
    public async Task SetUp()
    {
        _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
        await SeedDbContext(_context);
        _mockFileService = new Mock<IFileService>();
        _logger = new Mock<ILogger<Handler>>();
        _handler = new Handler(_context, _mockFileService.Object, _logger.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _context.Dispose();
    }

    [Test]
    public async Task GetSubmissionResult_ShouldReturnOk_WhenSubmissionExists()
    {
        var query = new GetSubmissionReviewQuery
        {
            SubmissionId = 10
        };
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());
        Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
        Assert.That(result.Value, Is.Not.Null);
    }

    [Test]
    public async Task GetSubmissionResult_ShouldReturnNotFound_WhenSubmissionNotExists()
    {
        var query = new GetSubmissionReviewQuery
            { SubmissionId = 11 };
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());
        Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound));
    }

    private static async Task SeedDbContext(IExamManagementDbContext dataContext)
    {
        await dataContext.Submissions.AddAsync(new Submission
        {
            Id = 10,
            ExaminerId = 1,
            ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f")
        });
        await dataContext.ExamVersions.AddAsync(new ExamVersion
        {
            Id = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
            VersionNumber = "1",
            CreatedDate = DateTime.Now,
            CreatedByUserId = "a"
        });
        await dataContext.ExamSections.AddAsync(new ExamSection
        {
            Id = 1,
            HeaderTitle = "a",
            ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f")
        });
        await dataContext.Questions.AddAsync(new Question
        {
            Id = 1,
            Title = "Test",
            ExamSectionId = 1,
            CreatedDate = DateTime.Now,
            CreatedByUserId = "a"
        });
        await dataContext.Questions.AddAsync(new Question
        {
            Id = 2,
            Title = "Test",
            ExamSectionId = 1,
            CreatedDate = DateTime.Now,
            CreatedByUserId = "a"
        });
        await dataContext.Answers.AddAsync(new Answer
        {
            Id = 1,
            QuestionId = 1,
            IsCorrect = true
        });

        await dataContext.SaveChangesAsync();
    }
}