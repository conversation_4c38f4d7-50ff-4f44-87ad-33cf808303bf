﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models.CQRS;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.Exam.Audit.Contracts.Messages;
using S3.ExamManagement.Application.Examiners.Commands.RegisterExaminer;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.Examiners.Commands.RegisterExaminer;

[TestFixture]
public class RegisterExaminerHandlerTests
{
    [SetUp]
    public async Task SetUp()
    {
        _dbContext = TestUtility.CreateDbContext<ExamManagementDbContext>();
        await _dbContext.Database.EnsureCreatedAsync();
        await SeedDatabase(_dbContext);

        _mockAuditService = new Mock<IAuditService>();
        _mockLogger = new Mock<ILogger<Handler>>();

        _handler = new Handler(
            _dbContext,
            _mockAuditService.Object,
            _mockLogger.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _dbContext?.Dispose();
    }

    private ExamManagementDbContext _dbContext;
    private Mock<IAuditService> _mockAuditService;
    private Mock<ILogger<Handler>> _mockLogger;
    private Handler _handler;

    private static async Task SeedDatabase(ExamManagementDbContext dbContext)
    {
        dbContext.Examiners.AddRange(new Examiner
        {
            Email = "<EMAIL>",
            DisplayName = "Existing Examiner",
            PhoneNumber = "1234567890",
            CreatedByUserId = "testUser"
        });
        await dbContext.SaveChangesAsync();
    }

    [Test]
    public async Task Handle_ShouldRegisterExaminer_WhenEmailIsUnique()
    {
        // Arrange
        var command = new RegisterExaminerCommand
        {
            DisplayName = "New Examiner",
            Email = "<EMAIL>",
            PhoneNumber = "9876543210",
            Header = new RequestHeader
            {
                UserId = "TestUser",
                UserName = "user",
                Roles = null
            }
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        var registeredExaminer = await _dbContext.Examiners.FirstOrDefaultAsync(e => e.Email == command.Email);

        Assert.Multiple(() =>
        {
            Assert.That(result.Value, Is.Not.Null, "Expected a non-null result.");
            Assert.That(result?.Status, Is.EqualTo(ResultStatus.Ok), "Operation should be successful.");
            Assert.That(registeredExaminer, Is.Not.Null, "Examiner should be registered.");
            Assert.That(registeredExaminer!.Email, Is.EqualTo(command.Email), "Email should match.");
        });

        // Ensure audit log is recorded
        _mockAuditService.Verify(x => x.Audit(It.IsAny<AuditLogMessage>()), Times.Once);
    }

    [Test]
    public async Task Handle_ShouldReturnOk_WhenEmailAlreadyExists()
    {
        // Arrange
        var command = new RegisterExaminerCommand
        {
            DisplayName = "Duplicate Examiner",
            Email = "<EMAIL>",
            PhoneNumber = "1112223333",
            Header = new RequestHeader
            {
                UserId = "TestUser",
                UserName = "user",
                Roles = null
            }
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
            Assert.That(result.Value, Is.Not.Null, "Expected a non-null result.");
            Assert.That(result.Value?.ExaminerId, Is.GreaterThan(0), "examiner Id should be greater than zero.");
        });
    }
}