﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using S3.ExamManagement.Application.Account.Queries.AdminLogin;
using Microsoft.Extensions.Options;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.ExamManagement.Application.Helpers.Implementations;

namespace S3.ExamManagement.UnitTest.Accounts.Queries.AdminLogin
{
    [TestFixture]
    internal class HandlerTest
    {
        private Mock<IOptions<LoginData>> _options = new Mock<IOptions<LoginData>> { DefaultValue = DefaultValue.Mock };
        private Handler _handler = default!;

        [SetUp]
        public void SetUp()
        {
            FillAppSettings();
            _handler = new Handler(_options.Object);
        }

        [TearDown]
        public void TearDown()
        {

        }

        [Test]
        public async Task AdminLogin_ShouldLoginSuccessfully()
        {
            var userName = _options.Object.Value.UserName;
            var password = _options.Object.Value.Password;

            var query = new AdminLoginQuery { UserName = userName ?? "", Password = password ?? "" };
            var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());


            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
                Assert.That(result.Value?.IsAuthenticated, Is.EqualTo(true));
            });
        }

        [Test]
        public async Task AdminLogin_WrongUserName_BadRequest()
        {
            var password = _options.Object.Value.Password;

            var query = new AdminLoginQuery { UserName = "ba", Password = password ?? "" };
            var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());


            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.BadRequest));
            });
        }

        [Test]
        public async Task AdminLogin_WrongPassword_BadRequest()
        {
            var userName = _options.Object.Value.UserName;

            var query = new AdminLoginQuery { UserName = userName ?? "", Password = "password" };
            var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());


            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.BadRequest));
            });
        }

        private void FillAppSettings()
        {
            var loginData = new LoginData { UserName = "Admin", Password = "P@ssw0rd" };
            var mockSection = new Mock<IOptions<LoginData>>();

            // Set up the section to return a specific value
            mockSection.Setup(x => x.Value).Returns(loginData);
            _options.Setup(x => x.Value).Returns(loginData);
        }
    }
}
