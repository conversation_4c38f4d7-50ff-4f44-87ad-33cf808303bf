﻿using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.ExamCategories.Queries.GetExamCategoriesWithExams;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Domain.Enums;
using S3.ExamManagement.Infrastructures.Services.Persistence;
using static NUnit.Framework.Assert;

namespace S3.ExamManagement.UnitTest.ExamCategories.Queries.GetExamCategoriesWithExams;

internal class HandlerTest
{
    private ExamManagementDbContext _context = default!;
    private Mock<IFileService> _fileService = default!;
    private Handler _handler = default!;
    private Mock<ILogger<Handler>> _logger = default!;
    private Mock<IMediator> _mediator = default!;


    [SetUp]
    public async Task SetUp()
    {
        _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
        await SeedDbContext(_context);


        _logger = new Mock<ILogger<Handler>>();
        _fileService = new Mock<IFileService>();
        _mediator = new Mock<IMediator>();

        // Mock file service to return expected URLs
        _fileService.Setup(fs => fs.GetImageUrl(It.IsAny<string>(), It.IsAny<string>()))
            .Returns((string imageName, string category) => $"https://cdn.example.com/{category}/{imageName}");

        _handler = new Handler(_context, _logger.Object, _fileService.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _context.Dispose();
    }

    #region SeedData

    private static async Task SeedDbContext(IExamManagementDbContext dataContext)
    {
        var categories = new List<ExamCategory>
        {
            new()
            {
                Name = "ActiveCategory",
                Description = "Active category with exams",
                DisplayOrder = 1, ImageName = "image.jpg",
                IsActive = true,
                Exams = new List<Domain.Entities.Exam>
                {
                    new()
                    {
                        Name = "Math1",
                        Description = "Mathematics Exam 1",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = true,
                        ImageName = "image.jpg",
                        PassPercentage = 70.0m,
                        DurationInMinutes = 60,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "a",
                        ExamVersions = new List<ExamVersion>
                        {
                            new (){Id=Guid.NewGuid(),VersionNumber = "1.0",Status = ExamVersionStatus.Active,TotalMarks = 200,CreatedByUserId = "b",CreatedDate = DateTime.UtcNow},
                            new (){Id=Guid.NewGuid(),VersionNumber = "2.0",Status = ExamVersionStatus.Active,TotalMarks = 200,CreatedByUserId = "b",CreatedDate = DateTime.UtcNow}
                        }
                    },
                    new()
                    {
                        Name = "Science1",
                        Description = "Science Exam 1",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = false,
                        PassPercentage = 60.0m,
                        DurationInMinutes = 90,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "a"
                    },
                    new()
                    {
                        Name = "History1",
                        Description = "History Exam 1",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = true,
                        PassPercentage = 80.0m, ImageName = "image.jpg",
                        DurationInMinutes = 120,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "a"
                    }
                },
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "a"
            },
            new()
            {
                Name = "InactiveCategory",
                Description = "Inactive category with exams",
                DisplayOrder = 2,
                IsActive = false,
                Exams = new List<Domain.Entities.Exam>
                {
                    new()
                    {
                        Name = "Math2",
                        Description = "Mathematics Exam 2",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = false,
                        PassPercentage = 75.0m,
                        DurationInMinutes = 45,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "b"
                    },
                    new()
                    {
                        Name = "Science2",
                        Description = "Science Exam 2",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = true,
                        PassPercentage = 65.0m,
                        DurationInMinutes = 60, ImageName = "image.jpg",
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "b"
                    },
                    new()
                    {
                        Name = "History2",
                        Description = "History Exam 2",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = false,
                        PassPercentage = 60.0m,
                        DurationInMinutes = 90,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "b"
                    }
                },
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "b"
            }
        };


        await dataContext.ExamCategories.AddRangeAsync(categories);
        await dataContext.SaveChangesAsync();
    }

    #endregion


    private GetExamCategoriesWithExamsQuery CreateQuery(int pageNumber = 1, int pageSize = 10, int itemPerPage = 4)
    {
        return new GetExamCategoriesWithExamsQuery
        {
            ItemPerPage = itemPerPage,
            PageSize = pageSize,
            PageNumber = pageNumber,
            Header = new RequestHeader
            {
                UserId = "user",
                UserName = "test",
                Roles = null
            }
        };
    }

    [Test]
    public async Task GetAllCategories_ShouldReturnOnlyActiveCategories_WithPublishedExams()
    {
        var query = CreateQuery();
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Ok), "Result status should be OK.");
            That(result.Value, Is.Not.Null, "Result value should not be null.");
            That(result.Value!.ExamCategories, Is.Not.Null.And.Not.Empty,
                "Exam categories should not be null or empty.");
            That(result.Value.ExamCategories.All(ec => ec.IsActive), Is.True, "All Exam Categories should be active.");
            That(result.Value.ExamCategories.All(ec => ec.Exams.All(ex => ex.IsPublished)), Is.True,
                "All Exams should be published.");
        });

        // Verify file service was used to retrieve images
        _fileService.Verify(fs => fs.GetImageUrl(It.IsAny<string>(), "ExamCategoryImages"));
        _fileService.Verify(fs => fs.GetImageUrl(It.IsAny<string>(), "ExamImages"));
    }

    [Test]
    public async Task GetAllCategories_ShouldReturnEmptyList_WhenNoActiveExamCategories()
    {
        await SetActiveStatusForAllCategories(false);

        var query = CreateQuery();
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Ok), "Result status should be OK.");
            That(result.Value, Is.Not.Null, "Result value should not be null.");
            That(result.Value!.ExamCategories, Is.Not.Null.And.Empty,
                "Exam categories should be empty when no active categories exist.");
        });
    }

    private async Task SetActiveStatusForAllCategories(bool isActive)
    {
        var categories = _context.ExamCategories.ToList();
        foreach (var category in categories) category.IsActive = isActive;
        _context.ExamCategories.UpdateRange(categories);
        await _context.SaveChangesAsync();
    }
}