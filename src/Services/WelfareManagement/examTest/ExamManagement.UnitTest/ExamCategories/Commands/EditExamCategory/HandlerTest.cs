﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Infrastructures.Services.Persistence;
using S3.ExamManagement.Application.Localization;
using S3.ExamManagement.Application.ExamCategories.Commands.EditExamCategories;

namespace S3.ExamManagement.UnitTest.ExamCategories.Commands.EditExamCategory
{
    [TestFixture]
    internal class HandlerTest
    {
        private ExamManagementDbContext _context = default!;
        private Mock<IAuditService> _mockAuditService = new Mock<IAuditService> { DefaultValue = DefaultValue.Mock };
        private Mock<ILogger<Handler>> _logger = new Mock<ILogger<Handler>> { DefaultValue = DefaultValue.Mock };
        private Mock<IFileService> _fileService = new Mock<IFileService> { DefaultValue = DefaultValue.Mock };
        private Handler _handler = default!;
        const string folderName = "ExamCategoryImages";

        [SetUp]
        public async Task SetUp()
        {
            _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
            await SeedDBContext(_context);
            _handler = new Handler(_context, _mockAuditService.Object, _fileService.Object, _logger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _context.Dispose();
        }

        [Test]
        public async Task EditExamCategory_ShouldEditSuccessfully()
        {
            var command = new EditExamCategoryCommand { Id = 1, Name = "EditCategory", Description = "EditDescription", Image = null };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            var editedExamCategory = await _context.ExamCategories.FirstOrDefaultAsync(x => x.Name == command.Name);
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
                Assert.That(editedExamCategory, Is.Not.Null);
                Assert.That(editedExamCategory?.Name, Is.EqualTo(command.Name));
                Assert.That(editedExamCategory?.Description, Is.EqualTo(command.Description));
            });
        }

        [Test]
        public async Task EditExamCategory_ShouldReturnConflict_WhenNameExists()
        {
            var command = new EditExamCategoryCommand { Id = 2, Name = "Pharmacology", 
                Description = "the branch of medicine concerned with the uses, effects, and modes of action of drugs." };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Conflict));
        }

        [Test]
        public async Task EditExamCategory_ShouldEditImage_WhenImageProvided()
        {
            var mockFile = new Mock<IFormFile>();
            _fileService.Setup(x => x.EditUploadedFile(It.IsAny<IFormFile>(), "02f1b308-2d5e-4a01-8f58-9dd2e3e60192.jpg",
                folderName, It.IsAny<CancellationToken>())).ReturnsAsync("test_image.png");
            var command = new EditExamCategoryCommand { Id = 1, Name = "Image", Description = "has Image", Image = mockFile.Object };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            var editedExamCategory = await _context.ExamCategories.FirstOrDefaultAsync(x => x.Name == command.Name);
            Assert.That(editedExamCategory?.ImageName, Is.EqualTo("test_image.png"));
        }

        [Test]
        public async Task EditExamCategory_CategoryDoesNotExit_NotFound()
        {
            var command = new EditExamCategoryCommand
            {
                Id = 2,
                Name = "doesn't even exist"
            };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.Multiple(() =>
            {
                Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound));
                //Assert.That(result.ErrorMessage, Is.EqualTo(Resources.CategoryNotFound));
            });
        }

        private static async Task SeedDBContext(IExamManagementDbContext dataContext)
        {
            await dataContext.ExamCategories.AddAsync(new Domain.Entities.ExamCategory
            {
                Id = 1,
                Name = "Pharmacology",
                Description = "the branch of medicine concerned with the uses, effects, and modes of action of drugs.",
                DisplayOrder = 1,
                IsActive = true,
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "Sohada",
                ImageName = "02f1b308-2d5e-4a01-8f58-9dd2e3e60192.jpg"
            });

            await dataContext.SaveChangesAsync();
        }
    }
}
