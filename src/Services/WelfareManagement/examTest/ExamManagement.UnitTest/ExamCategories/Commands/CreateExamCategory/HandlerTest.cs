﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.ExamCategories.Commands.CreateExamCategory;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.ExamCategories.Commands.CreateExamCategory
{
    internal class HandlerTest
    {
        private ExamManagementDbContext _context = default!;
        private Mock<IAuditService> _mockAuditService = default!;
        private Mock<ILogger<Handler>> _logger = default!;
        private Mock<IFileService> _fileService = default!;
        private Handler _handler = default!;

        [SetUp]
        public async Task SetUp()
        {
            _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
            await SeedDBContext(_context);
            _mockAuditService = new Mock<IAuditService>();
            _logger = new Mock<ILogger<Handler>>();
            _fileService = new Mock<IFileService>();
            _handler = new Handler(_context, _mockAuditService.Object, _logger.Object, _fileService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _context.Dispose();
        }

        [Test]
        public async Task CreateExamCategory_ShouldCreateSuccessfully()
        {
            var command = new CreateExamCategoryCommand { Name = "TestCategory", Description = "TestDescription", ImageFile = null };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
        }

        [Test]
        public async Task CreateExamCategory_ShouldReturnConflict_WhenNameExists()
        {
            var command = new CreateExamCategoryCommand { Name = "ExistingCategory", Description = "Description" };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Conflict));
        }

        [Test]
        public async Task CreateExamCategory_ShouldSaveImage_WhenImageProvided()
        {
            var mockFile = new Mock<IFormFile>();
            _fileService.Setup(x => x.SaveImageAsync(It.IsAny<IFormFile>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync("test_image.png");
            var command = new CreateExamCategoryCommand { Name = "ImageCategory", Description = "With Image", ImageFile = mockFile.Object };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            var createdExamCategory = await _context.ExamCategories.FirstOrDefaultAsync(x => x.Name == command.Name);
            Assert.That(createdExamCategory?.ImageName, Is.EqualTo("test_image.png"));
        }
        private static async Task SeedDBContext(IExamManagementDbContext dataContext)
        {
            await dataContext.ExamCategories.AddAsync(new ExamCategory
            {
                Name = "ExistingCategory",
                Description = "Pre-existing category",
                DisplayOrder = 1,
                IsActive = true,
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "a"
            });

            await dataContext.SaveChangesAsync();
        }

    }
}
