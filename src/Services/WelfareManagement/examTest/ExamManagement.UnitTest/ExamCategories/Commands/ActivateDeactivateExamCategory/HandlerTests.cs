﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.Exam.Audit.Contracts.Messages;
using S3.ExamManagement.Application.ExamCategories.Commands.ActivateDeactivateExamCategory;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.ExamCategories.Commands.ActivateDeactivateExamCategory
{
    [TestFixture]
    public class ActivateDeactivateExamCategoryHandlerTests
    {
        private ExamManagementDbContext _dbContext;
        private Mock<IAuditService> _mockAuditService;
        private Mock<ILogger<Handler>> _mockLogger;
        private Handler _handler;

        [SetUp]
        public async Task SetUp()
        {
            _dbContext = TestUtility.CreateDbContext<ExamManagementDbContext>();
            await _dbContext.Database.EnsureCreatedAsync();
            await SeedDatabase(_dbContext);

            _mockAuditService = new Mock<IAuditService>();
            _mockLogger = new Mock<ILogger<Handler>>();

            _handler = new Handler(
                _dbContext,
                _mockAuditService.Object,
                _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _dbContext?.Dispose(); // Ensure database is disposed properly
        }

        private static async Task SeedDatabase(ExamManagementDbContext dbContext)
        {
            dbContext.ExamCategories.AddRange(new[]
            {
                new Domain.Entities.ExamCategory
                {
                    Id = 1,
                    Name = "Math",
                    IsActive = false,
                    CreatedByUserId = "testUser"
                },
                new Domain.Entities.ExamCategory
                {
                    Id = 2,
                    Name = "Science",
                    IsActive = true,
                    CreatedByUserId = "testUser"
                }
            });

            await dbContext.SaveChangesAsync();
        }

        [Test]
        public async Task Handle_ShouldActivateDeactivateExamCategory_WhenCategoryExists()
        {
            // Arrange
            var examCategory = await _dbContext.ExamCategories.FirstAsync();
            var initialIsActive = examCategory.IsActive;
            var command = new ActivateDeactivateExamCategoryCommand
            {
                ExamCategoryId = examCategory.Id,
                Header = new Core.Application.Models.CQRS.RequestHeader
                {
                    UserId = "TestUser",
                    UserName = "user",
                    Roles = null
                }
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            var updatedCategory = await _dbContext.ExamCategories.FindAsync(examCategory.Id);
            Assert.Multiple(() =>
            {
                Assert.That(result.Value, Is.Not.Null, "Expected a non-null result.");
                Assert.That(result?.Value?.IsSuccess, Is.True, "Operation should be successful.");
                Assert.That(updatedCategory, Is.Not.Null, "Updated category should exist.");
                Assert.That(updatedCategory!.IsActive, Is.Not.EqualTo(initialIsActive), "Activation status should toggle.");
                Assert.That(updatedCategory.UpdatedUserId, Is.EqualTo(command.Header.UserId), "UpdatedUserId should match.");
                Assert.That(updatedCategory.UpdatedDate, Is.Not.Null, "UpdatedDate should be set.");
            });



            // Ensure audit log is recorded
            _mockAuditService.Verify(x => x.Audit(It.IsAny<AuditLogMessage>()), Times.Once);
        }

        [Test]
        public async Task Handle_ShouldReturnNotFound_WhenCategoryDoesNotExist()
        {
            // Arrange
            var command = new ActivateDeactivateExamCategoryCommand
            {
                ExamCategoryId = int.MaxValue, // Ensure ID does not exist
                Header = new Core.Application.Models.CQRS.RequestHeader
                {
                    UserId = "TestUser",
                    UserName = "null",
                    Roles = null
                }
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound), "Expected NotFound result.");
                Assert.That(result.ErrorMessage, Is.Not.Null, "Expected an error message.");
            });


        }

    }
}