﻿using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.Exam.Audit.Contracts.Messages;
using S3.ExamManagement.Application.ExamCategories.Commands.DeleteExamCategory;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;
using Handler = S3.ExamManagement.Application.ExamCategories.Commands.DeleteExamCategory.Handler;

namespace S3.ExamManagement.UnitTest.ExamCategories.Commands.DeleteExamCategory;

internal class HandlerTest
{
    private Mock<IAuditService> _auditServiceMock = null!;
    private IExamManagementDbContext _examManagementDbContext;
    private Mock<ILogger<Handler>> _mockILogger = null!;

    [SetUp]
    public async Task SetUp()
    {
        _examManagementDbContext = TestUtility.CreateDbContext<ExamManagementDbContext>();
        await SeedDBContext(_examManagementDbContext);

        _auditServiceMock = new Mock<IAuditService>();
        _mockILogger = new Mock<ILogger<Handler>>();

        _auditServiceMock.Setup(x => x.Audit(It.IsAny<AuditLogMessage>())).Returns(Task.CompletedTask);
    }

    [Test]
    public async Task DeleteExamCategory_ShouldSuccess_WhenNoError()
    {
        // arrange
        var handler = CreateHandler();
        var request = new DeleteExamCategoryCommand
        {
            Header = new RequestHeader
            {
                UserId = "1",
                UserName = "user",
                Roles = null
            },
            Id = 2
        };

        //act
        var result = await handler.Handle(request, AppUtility.CreateCancelationToken());

        //assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Value, Is.Not.Null);
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
        });
    }

    [Test]
    public async Task DeleteExamCategory_ShouldReturnNotFound_WhenInvalidExamId()
    {
        // arrange
        var handler = CreateHandler();
        var request = new DeleteExamCategoryCommand
        {
            Header = new RequestHeader
            {
                UserId = "1",
                UserName = "user",
                Roles = null
            },
            Id = 4
        };

        //act
        var result = await handler.Handle(request, AppUtility.CreateCancelationToken());

        //assert
        Assert.Multiple(() => { Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound)); });
    }

    [Test]
    public async Task DeleteExamCategory_ShouldReturnBadRequest_WhenExamCategoryHasExams()
    {
        // arrange
        var handler = CreateHandler();
        var request = new DeleteExamCategoryCommand
        {
            Header = new RequestHeader
            {
                UserId = "1",
                UserName = "user",
                Roles = null
            },
            Id = 3
        };

        //act
        var result = await handler.Handle(request, AppUtility.CreateCancelationToken());

        //assert
        Assert.Multiple(() => { Assert.That(result.Status, Is.EqualTo(ResultStatus.BadRequest)); });
    }

    [TearDown]
    public void TearDown()
    {
        if (_examManagementDbContext is IDisposable disposableDbContext) disposableDbContext.Dispose();
    }

    private Handler CreateHandler()
    {
        return new Handler(
            _examManagementDbContext,
            _auditServiceMock.Object,
            _mockILogger.Object
        );
    }

    private static async Task SeedDBContext(IExamManagementDbContext dataContext)
    {
        ExamCategory[] examCategories =
        [
            new()
            {
                Id = 1,
                Name = "cat1",
                DisplayOrder = 1,
                IsActive = false,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now
            },
            new()
            {
                Id = 2,
                Name = "cat2",
                DisplayOrder = 2,
                IsActive = true,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now
            },
            new()
            {
                Id = 3,
                Name = "cat3",
                DisplayOrder = 3,
                IsActive = true,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now,
                Exams = new List<Domain.Entities.Exam>
                {
                    new()
                    {
                        Id = Guid.NewGuid(),
                        Name = "Exam1",
                        Description = "test",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.Now),
                        PassPercentage = .50m,
                        DurationInMinutes = 100,
                        ExamCategoryId = 3,
                        CreatedByUserId = "1",
                        CreatedDate = DateTime.Now
                    }
                }
            }
        ];

        dataContext.ExamCategories.AddRange(examCategories);
        await dataContext.SaveChangesAsync();
    }
}