﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.Exams.Queries.GetExamDetails;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Domain.Enums;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.Exams.Queries.GetExamVersionsByExamId
{
    [TestFixture]
    internal class HandlerTest
    {
        private ExamManagementDbContext _context = default!;
        private Mock<ILogger<Handler>> _logger = new Mock<ILogger<Handler>> { DefaultValue = DefaultValue.Mock };
        private Mock<IFileService> _fileService = new Mock<IFileService> { DefaultValue = DefaultValue.Mock };
        private Handler _handler = default!;

        [SetUp]
        public async Task SetUp()
        {
            _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
            await SeedDBContext(_context);
            _handler = new Handler(_context, _logger.Object, _fileService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _context.Dispose();
        }

        [Test]
        public async Task GetExamDetails_ShouldFetchSuccessfully()
        {
            var query = new GetExamDetailsQuery
            {
                ExamId = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310")
            };
            var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());
            var getExamDetails = await _context.Exams.FirstOrDefaultAsync(x => x.Id == query.ExamId && x.IsPublished == true);
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
                Assert.That(getExamDetails, Is.Not.Null);
                Assert.That(getExamDetails?.Id, Is.EqualTo(query.ExamId));
                Assert.That(getExamDetails?.ExamVersions.Count, Is.EqualTo(2));
            });
        }

        [Test]
        public async Task GetExamDetails_ShouldReturnNotFound_WhenExamIdNotInDb()
        {
            var query = new GetExamDetailsQuery
            {
                ExamId = Guid.NewGuid()
            };
            var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound));
        }

        private static async Task SeedDBContext(IExamManagementDbContext dataContext)
        {
            await dataContext.Exams.AddAsync(new Domain.Entities.Exam
            {
                CreatedByUserId = "1",
                DurationInMinutes = 60,
                EndDate = new DateOnly(2025, 3, 1),
                ExamCategoryId = 1,
                Id = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310"),
                ImageName = "r1.jpeg",
                IsPublished = true,
                Name = "Thyroid",
                PassPercentage = 0.50m,
                StartDate = new DateOnly(2025, 2, 25),
                ExamVersions = new List<ExamVersion>
                {
                    new ExamVersion
                    {
                        Id = new Guid("776189ed-7cbb-4544-bf3e-641d7201eb3b"),
                        CreatedByUserId = "1",
                        Status = ExamVersionStatus.Active,
                        TotalMarks = 100,
                        VersionNumber = "0.1"
                    },
                    new ExamVersion
                    {
                        Id = new Guid("6c4345b5-f626-4bcf-aafa-a028a05f8ccd"),
                        CreatedByUserId = "1",
                        Status = ExamVersionStatus.Active,
                        TotalMarks = 150,
                        VersionNumber = "0.2"
                    }
                }
            });

            await dataContext.SaveChangesAsync();
        }
    }
}
