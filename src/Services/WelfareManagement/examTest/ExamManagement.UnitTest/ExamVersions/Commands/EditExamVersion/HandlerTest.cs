﻿using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.ExamVersions.Commands.EditExamVersion;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Domain.Enums;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.ExamVersions.Commands.EditExamVersion
{
    [TestFixture]
    internal class HandlerTest
    {
        private ExamManagementDbContext _context = default!;
        private Mock<IAuditService> _mockAuditService = default!;
        private Mock<ILogger<Handler>> _logger = default!;
        private Mock<IFileService> _fileService = default!;
        private Handler _handler = default!;

        [SetUp]
        public async Task SetUp()
        {
            _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
            await SeedDBContext(_context);
            _mockAuditService = new Mock<IAuditService>();
            _logger = new Mock<ILogger<Handler>>();
            _fileService = new Mock<IFileService>();
            _handler = new Handler(_context, _mockAuditService.Object, _fileService.Object, _logger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _context.Dispose();
        }

        [Test]
        public async Task EditExamVersion_ShouldCreateSuccessfully()
        {
            var command = new EditExamVersionCommand
            {
                Id = Guid.Parse("5de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
                VersionNumber = "V2",
                ExamSections = new List<ExamSectionItem>
            {
                new ExamSectionItem
                {
                    Id = 1, HeaderTitle = "Updated Math", DisplayOrder = 1, ColorId = 2,
                    Questions = new List<QuestionItem>
                    {
                        new QuestionItem
                        {
                            Id = 1, Mark = 12, DisplayOrder = 1, QuestionType = QuestionType.SingleChoice,
                            Answers = new List<AnswerItem>
                            {
                                new AnswerItem { Id = 1, AnswerText = "C", IsCorrect = true },
                                new AnswerItem { Id = 2, AnswerText = "D", IsCorrect = false }
                            }
                        }
                    }
                },
                new ExamSectionItem
                {
                    Id = 3, HeaderTitle = "History", DisplayOrder = 3, ColorId = 4,
                    Questions = new List<QuestionItem>
                    {
                        new QuestionItem
                        {
                            Mark = 20, DisplayOrder = 1, QuestionType = QuestionType.MultipleChoice,
                            Answers = new List<AnswerItem>
                            {
                                new AnswerItem { Id = 5, AnswerText = "Yes", IsCorrect = true },
                                new AnswerItem { Id = 6, AnswerText = "No", IsCorrect = false },
                                new AnswerItem { AnswerText = "No", IsCorrect = false },
                            }
                        }
                    }
                }
            }
            };

            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
        }

        [Test]
        public async Task EditExamVersion_ShouldReturnNotFound_WhenIdNotExist()
        {
            var command = new EditExamVersionCommand
            {
                Id = Guid.Parse("2de396c8-c2ef-4ab3-8cb6-0b8226d0889f"), // Must match seeded data
                VersionNumber = "V1", // Required, max 10 chars
                ExamSections = new List<ExamSectionItem>
    {
        new ExamSectionItem
        {
            Id = 1,
            ColorId = 1, // Required
            HeaderTitle = "Math Section", // Required
            DisplayOrder = 1, // Required
            Questions = new List<QuestionItem>
            {
                new QuestionItem
                {
                    Id = 1,
                    Mark = 10, // Required
                    DisplayOrder = 1, // Required
                    QuestionType = QuestionType.SingleChoice, // Required
                    Answers = new List<AnswerItem>
                    {
                        new AnswerItem
                        {
                            Id = 1,
                            AnswerText = "Answer 1", // Required if not FigureSelection
                            IsCorrect = true // At least one must be correct
                        }
                    }
                }
            }
        }
    },
                ExamVersionResultFormats = new List<ExamVersionResultFormatItem>
    {
        new ExamVersionResultFormatItem
        {
            ResultFormatId = 1, // Required
            DescriptiveResults = new List<DescriptiveResultItem>
            {
                new DescriptiveResultItem
                {
                    Message = "Pass", // Required
                    MinMark = 1,
                    MaxMark = 10
                }
            },
            GradeRanges = new List<GradeRangeItem>
            {
                new GradeRangeItem
                {
                    Grade = "A", // Either Grade or Image required
                    MinMark = 1,
                    MaxMark = 10
                }
            }
        }
    }
            };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound));
        }

        [Test]
        public async Task EditExamVersion_ShouldReturnBadRequest_WhenVersionHaveSubbmissions()
        {

            var command = new EditExamVersionCommand
            {
                Id = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"), // Must match seeded data
                VersionNumber = "V1", // Required, max 10 chars
                ExamSections = new List<ExamSectionItem>
    {
        new ExamSectionItem
        {
            Id = 1,
            ColorId = 1, // Required
            HeaderTitle = "Math Section", // Required
            DisplayOrder = 1, // Required
            Questions = new List<QuestionItem>
            {
                new QuestionItem
                {
                    Id = 1,
                    Mark = 10, // Required
                    DisplayOrder = 1, // Required
                    QuestionType = QuestionType.SingleChoice, // Required
                    Answers = new List<AnswerItem>
                    {
                        new AnswerItem
                        {
                            Id = 1,
                            AnswerText = "Answer 1", // Required if not FigureSelection
                            IsCorrect = true // At least one must be correct
                        }
                    }
                }
            }
        }
    },
                ExamVersionResultFormats = new List<ExamVersionResultFormatItem>
    {
        new ExamVersionResultFormatItem
        {
            ResultFormatId = 1, // Required
            DescriptiveResults = new List<DescriptiveResultItem>
            {
                new DescriptiveResultItem
                {
                    Message = "Pass", // Required
                    MinMark = 1,
                    MaxMark = 10
                }
            },
            GradeRanges = new List<GradeRangeItem>
            {
                new GradeRangeItem
                {
                    Grade = "A", // Either Grade or Image required
                    MinMark = 1,
                    MaxMark = 10
                }
            }
        }
    }
            };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.BadRequest));
        }
        private static async Task SeedDBContext(IExamManagementDbContext dataContext)
        {
            var examVersions = new List<ExamVersion>
        {
            new ExamVersion
            {
                Id= Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
                CreatedByUserId="user",
                CreatedDate= DateTime.Now,
                Status=ExamVersionStatus.InActive,
                TotalMarks=0,
                VersionNumber="1",
                DurationInMinutes=null,
                Exam=new Domain.Entities.Exam{ Id=Guid.Parse("6de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),DurationInMinutes=10,Description="asd",CreatedByUserId="a",Name="a"},
                Submissions=new List<Submission>{ new Submission {Id=1} }
            },
            new ExamVersion
            {
                Id = Guid.Parse("5de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
                CreatedByUserId = "user",
                CreatedDate = DateTime.Now,
                Status = ExamVersionStatus.InActive,
                TotalMarks = 100,
                VersionNumber = "1",
                DurationInMinutes = 60,
                Exam = new Domain.Entities.Exam { Id = Guid.Parse("7de396c8-c2ef-4ab3-8cb6-0b8226d0889f"), DurationInMinutes = 10, Description = "Test Exam", CreatedByUserId = "a", Name = "Exam 1" },
                ExamSections = new List<ExamSection>
                {
                    new ExamSection
                    {
                        Id = 1, HeaderTitle = "Math", DisplayOrder = 1, ColorId = 2,
                        Questions = new List<Question>
                        {
                            new Question
                            {
                                Id = 1, DisplayOrder = 1, Mark = 10, QuestionType = QuestionType.SingleChoice,CreatedByUserId="a",
                                Answers = new List<Answer>
                                {
                                    new Answer { Id = 1, AnswerText = "A", IsCorrect = true },
                                    new Answer { Id = 2, AnswerText = "B", IsCorrect = false }
                                }
                            }
                        }
                    },
                    new ExamSection
                    {
                        Id = 2, HeaderTitle = "Science", DisplayOrder = 2, ColorId = 3,
                        Questions = new List<Question>
                        {
                            new Question
                            {
                                Id = 2, DisplayOrder = 1, Mark = 15, QuestionType = QuestionType.MultipleChoice,CreatedByUserId="a",
                                Answers = new List<Answer>
                                {
                                    new Answer { Id = 3, AnswerText = "True", IsCorrect = true },
                                    new Answer { Id = 4, AnswerText = "False", IsCorrect = false }
                                }
                            }
                        }
                    }
                }
            }
        };
            dataContext.ExamVersions.AddRange(examVersions);

            await dataContext.SaveChangesAsync();
        }

    }
}
