﻿using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.Exams.Queries.GetAllExamsByCategoryId;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;
using static NUnit.Framework.Assert;
using Handler = S3.ExamManagement.Application.Exams.Queries.GetAllExamsByCategoryId.Handler;

namespace S3.ExamManagement.UnitTest.Exam.Queries.GetAllExamsByCategoryId;

internal class HandlerTest
{
    private ExamManagementDbContext _context = default!;
    private Mock<IFileService> _fileService = default!;
    private Handler _handler = default!;
    private Mock<ILogger<Handler>> _logger = default!;
    private Mock<IMediator> _mediator = default!;
    private Mock<IAuditService> _mockAuditService = default!;

    [SetUp]
    public async Task SetUp()
    {
        _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
        await SeedDbContext(_context);

        _mockAuditService = new Mock<IAuditService>();
        _logger = new Mock<ILogger<Handler>>();
        _fileService = new Mock<IFileService>();
        _mediator = new Mock<IMediator>();

        // Mock file service to return expected URLs
        _fileService.Setup(fs => fs.GetImageUrl(It.IsAny<string>(), It.IsAny<string>()))
            .Returns((string imageName, string category) => $"https://cdn.example.com/{category}/{imageName}");

        _handler = new Handler(_context, _fileService.Object, _logger.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _context.Dispose();
    }

    #region SeedData

    private static async Task SeedDbContext(IExamManagementDbContext dataContext)
    {
        var categories = new List<ExamCategory>
        {
            new()
            {
                Id = 1,
                Name = "ActiveCategory",
                Description = "Active category with exams",
                DisplayOrder = 1, ImageName = "image.jpg",
                IsActive = true,
                Exams = new List<Domain.Entities.Exam>
                {
                    new()
                    {
                        Name = "Math1",
                        Description = "Mathematics Exam 1",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = true,
                        ImageName = "image.jpg",
                        PassPercentage = 70.0m,
                        DurationInMinutes = 60,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "a"
                    },
                    new()
                    {
                        Name = "Science1",
                        Description = "Science Exam 1",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = false,
                        PassPercentage = 60.0m,
                        DurationInMinutes = 90,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "a"
                    },
                    new()
                    {
                        Name = "History1",
                        Description = "History Exam 1",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = true,
                        PassPercentage = 80.0m, ImageName = "image.jpg",
                        DurationInMinutes = 120,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "a"
                    }
                },
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "a"
            },
            new()
            {
                Id = 2,
                Name = "InactiveCategory",
                Description = "Inactive category with exams",
                DisplayOrder = 2,
                IsActive = false,
                Exams = new List<Domain.Entities.Exam>
                {
                    new()
                    {
                        Name = "Math2",
                        Description = "Mathematics Exam 2",
                        ImageName = "image.jpg",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = false,
                        PassPercentage = 75.0m,
                        DurationInMinutes = 45,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "b"
                    },
                    new()
                    {
                        Name = "Science2",
                        Description = "Science Exam 2",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = true,
                        PassPercentage = 65.0m,
                        DurationInMinutes = 60, ImageName = "image.jpg",
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "b"
                    },
                    new()
                    {
                        Name = "History2",
                        Description = "History Exam 2",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddMonths(1)),
                        IsPublished = false,
                        PassPercentage = 60.0m,
                        DurationInMinutes = 90,
                        CreatedDate = DateTime.UtcNow,
                        CreatedByUserId = "b"
                    }
                },
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "b"
            }
        };


        await dataContext.ExamCategories.AddRangeAsync(categories);
        await dataContext.SaveChangesAsync();
    }

    #endregion


    private GetAllExamsByCategoryIdQuery CreateQuery(int examCategoryId, int pageNumber = 1, int pageSize = 10,
        int itemPerPage = 4)
    {
        return new GetAllExamsByCategoryIdQuery
        {
            PageSize = pageSize,
            ExamCategoryId = examCategoryId,
            PageNumber = pageNumber,
            Header = new RequestHeader
            {
                UserId = "user",
                UserName = "test",
                Roles = null
            }
        };
    }

    [Test]
    public async Task GetAllExams_ByCategoryId_ShouldReturnOk_WithPublishedExams_WhenCategoryExists()
    {
        var query = CreateQuery(1);
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Ok), "Result status should be OK.");
            That(result.Value, Is.Not.Null, "Result value should not be null.");
            //That(result.Value!.Exams, Is.Not.Null.And.Not.Empty,
            //    "Exams should not be null or empty.");
            //That(result.Value.Exams.Data.All(ec => ec.IsPublished), Is.True, "All Exams should be IsPublished.");
        });

        // Verify file service was used to retrieve images

        _fileService.Verify(fs => fs.GetImageUrl(It.IsAny<string>(), "ExamImages"));
    }

    [Test]
    public async Task GetAllExams_ByCategoryId_ShouldReturnNotFound_WhenNoCategoryExist()
    {
        var query = CreateQuery(3);
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.NotFound), "Result status should be OK.");
            That(result.Value, Is.Null, "Result value should  be null.");
        });
    }

    [Test]
    public async Task GetAllExams_ByCategoryId_ShouldReturnNotFound_WhenCategoryNotActive()
    {
        await SetActiveStatusForAllCategories(false);

        var query = CreateQuery(1);
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.NotFound), "Result status should be OK.");
            That(result.Value, Is.Null, "Result value should  be null.");
        });
    }

    private async Task SetActiveStatusForAllCategories(bool isActive)
    {
        var categories = _context.ExamCategories.ToList();
        foreach (var category in categories) category.IsActive = isActive;
        _context.ExamCategories.UpdateRange(categories);
        await _context.SaveChangesAsync();
    }
}