﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.Exam.Audit.Contracts.Messages;
using S3.ExamManagement.Application.Exams.Commands.EditExam;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Domain.Enums;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.UnitTest.Exam.Commands.EditExam;

internal class HandlerTest
{
    private ExamManagementDbContext _context = default!;
    private Handler _handler = default!;
    private Mock<IAuditService> _mockAuditService = default!;
    private Mock<IFileService> _mockFileService = default!;
    private Mock<ILogger<Handler>> _mockLogger = default!;

    [SetUp]
    public async Task SetUp()
    {
        _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
        await SeedDbContext(_context);
        _mockAuditService = new Mock<IAuditService>();
        _mockLogger = new Mock<ILogger<Handler>>();
        _mockFileService = new Mock<IFileService>();

        _handler = new Handler(_context, _mockFileService.Object, _mockAuditService.Object, _mockLogger.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _context.Dispose();
    }

    private static FormFile CreateImageFile()
    {
        // Create a random byte array representing a JPEG image
        var random = new Random();
        var imageBytes = new byte[1024]; // Size of the image in bytes (adjust as necessary)
        random.NextBytes(imageBytes); // Fill the byte array with random values

        var memoryStream = new MemoryStream(imageBytes);
        memoryStream.Position = 0; // Reset stream position to the beginning

        return new FormFile(memoryStream, 0L, imageBytes.Length, "formFile1", "test.jpg")
        {
            Headers = new HeaderDictionary(),
            ContentType = "image/jpeg" // Set correct content type for image
        };
    }

    [Test]
    public async Task Handle_ShouldEditExamSuccessfully_WhenExamExists()
    {
        var command = new EditExamCommand
        {
            ExamId = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310"),
            Name = "Updated Exam Name",
            Description = "Updated Description",
            DurationInMinutes = 120,
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(2),
            ImageFile = CreateImageFile()
        };
        _mockFileService.Setup(x =>
                x.SaveImageAsync(It.IsAny<IFormFile>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("new_image.png");
        var result = await _handler.Handle(command, CancellationToken.None);

        _mockAuditService.Verify(x => x.Audit(It.IsAny<AuditLogMessage>()), Times.Once);

        var updatedExam = await _context.Exams.FindAsync(command.ExamId);
        Assert.Multiple(() =>
        {
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
            Assert.That(updatedExam?.Name, Is.EqualTo(command.Name));
            Assert.That(updatedExam?.ImageName, Is.Not.Null);

            Assert.That(updatedExam?.Description, Is.EqualTo(command.Description));
        });
    }

    [Test]
    public async Task Handle_ShouldReturnNotFound_WhenExamDoesNotExist()
    {
        var command = new EditExamCommand
        {
            ExamId = Guid.NewGuid(), // Non-existent ID
            Name = "Non-Existent Exam"
        };

        var result = await _handler.Handle(command, CancellationToken.None);

        Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound));
    }


    private static async Task SeedDbContext(ExamManagementDbContext dataContext)
    {
        // Add test data (You can use an existing exam or create a new one)
        var exam = new Domain.Entities.Exam
        {
            Id = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310"),
            Name = "Sample Exam",
            Description = "Sample description",
            StartDate = DateOnly.FromDateTime(DateTime.UtcNow),
            EndDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(1)),
            PassPercentage = 70m,
            DurationInMinutes = 60,
            Direction = ExamDirection.Forward,
            CreatedDate = DateTime.UtcNow,
            CreatedByUserId = "user1"
        };
        await dataContext.Exams.AddAsync(exam);
        await dataContext.SaveChangesAsync();
    }
}