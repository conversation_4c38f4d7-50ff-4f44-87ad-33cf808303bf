﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.ExamManagement.Application.Helpers.Interfaces;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Application.Services.Persistence;
using S3.ExamManagement.Infrastructures.Services.Persistence;
using Microsoft.EntityFrameworkCore;
using S3.ExamManagement.Application.Exams.Commands.CreateExam;

namespace S3.ExamManagement.UnitTest.Exam.Commands.CreateExam
{
    [TestFixture]
    internal class HandlerTest
    {
        private ExamManagementDbContext _context = default!;
        private Mock<IAuditService> _mockAuditService = new Mock<IAuditService> { DefaultValue = DefaultValue.Mock };
        private Mock<ILogger<Handler>> _logger = new Mock<ILogger<Handler>> { DefaultValue = DefaultValue.Mock };
        private Mock<IFileService> _fileService = new Mock<IFileService> { DefaultValue = DefaultValue.Mock };
        private Handler _handler = default!;
        const string folderName = "ExamImages";

        [SetUp]
        public async Task SetUp()
        {
            _context = TestUtility.CreateDbContext<ExamManagementDbContext>();
            await SeedDBContext(_context);
            _handler = new Handler(_context, _mockAuditService.Object, _logger.Object, _fileService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _context.Dispose();
        }

        [Test]
        public async Task CreateExam_ShouldCreateSuccessfully()
        {
            var command = new CreateExamCommand
            {
                Name = "CreateExam",
                Description = "CreateDescription",
                Image = null,
                Direction = Domain.Enums.ExamDirection.Shuffle,
                DurationInMinutes = 60,
                EndDate = new DateOnly(2025, 12, 31),
                ExamCategoryId = 1,
                HasReview = true,
                PassPercentage = 0.50M,
                StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
            };

            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            var CreatedExam = await _context.Exams.FirstOrDefaultAsync(x => x.Name == command.Name && x.ExamCategoryId == command.ExamCategoryId);
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
                Assert.That(CreatedExam, Is.Not.Null);
                Assert.That(CreatedExam?.Name, Is.EqualTo(command.Name));
                Assert.That(CreatedExam?.Description, Is.EqualTo(command.Description));
            });
        }

        [Test]
        public async Task CreateExam_ShouldReturnConflict_WhenNameExists()
        {
            var command = new CreateExamCommand
            {
                Name = "Already in db",
                Description = "Already in db",
                Image = null,
                Direction = Domain.Enums.ExamDirection.Shuffle,
                DurationInMinutes = 60,
                EndDate = new DateOnly(2025, 12, 31),
                ExamCategoryId = 1,
                HasReview = true,
                PassPercentage = 0.50M,
                StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
            };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Conflict));
        }

        [Test]
        public async Task CreateExam_ShouldCreateSuccessfully_WhenSameNameExistsButUnderDifferentCategory()
        {
            var command = new CreateExamCommand
            {
                Name = "CreateExam",
                Description = "CreateDescription",
                Image = null,
                Direction = Domain.Enums.ExamDirection.Shuffle,
                DurationInMinutes = 60,
                EndDate = new DateOnly(2025, 12, 31),
                ExamCategoryId = 2,
                HasReview = true,
                PassPercentage = 0.50M,
                StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
            };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            var CreatedExam = await _context.Exams.FirstOrDefaultAsync(x => x.Name == command.Name && x.ExamCategoryId == command.ExamCategoryId);
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
                Assert.That(CreatedExam, Is.Not.Null);
                Assert.That(CreatedExam?.Name, Is.EqualTo(command.Name));
                Assert.That(CreatedExam?.Description, Is.EqualTo(command.Description));
            });
        }

        [Test]
        public async Task CreateExam_ShouldCreateImage_WhenImageProvided()
        {
            var mockFile = new Mock<IFormFile>();
            _fileService.Setup(x => x.SaveImageAsync(It.IsAny<IFormFile>(), folderName, It.IsAny<CancellationToken>()))
                .ReturnsAsync("test_image.png");
            var command = new CreateExamCommand
            {
                Name = "Image",
                Description = "has Image",
                Image = mockFile.Object,
                Direction = Domain.Enums.ExamDirection.Shuffle,
                DurationInMinutes = 60,
                EndDate = new DateOnly(2025, 12, 31),
                ExamCategoryId = 1,
                HasReview = true,
                PassPercentage = 0.50M,
                StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
            };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            var CreatedExam = await _context.Exams.FirstOrDefaultAsync(x => x.Name == command.Name);
            Assert.That(CreatedExam?.ImageName, Is.EqualTo("test_image.png"));
        }

        [Test]
        public async Task CreateExam_InActiveCategory()
        {
            var command = new CreateExamCommand
            {
                Name = "doesn't even exist",
                Description = "CreateDescription",
                Image = null,
                Direction = Domain.Enums.ExamDirection.Shuffle,
                DurationInMinutes = 60,
                EndDate = new DateOnly(2025, 12, 31),
                ExamCategoryId = 3,
                HasReview = true,
                PassPercentage = 0.50M,
                StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
            };
            var result = await _handler.Handle(command, AppUtility.CreateCancelationToken());
            Assert.Multiple(() =>
            {
                Assert.That(result.Status, Is.EqualTo(ResultStatus.Conflict));
            });
        }

        private static async Task SeedDBContext(IExamManagementDbContext dataContext)
        {
            await dataContext.ExamCategories.AddAsync(new Domain.Entities.ExamCategory
            {
                Id = 1,
                Name = "Pharmacology",
                Description = "the branch of medicine concerned with the uses, effects, and modes of action of drugs.",
                DisplayOrder = 1,
                IsActive = true,
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "Sohada",
                ImageName = "02f1b308-2d5e-4a01-8f58-9dd2e3e60192.jpg"
            });

            await dataContext.ExamCategories.AddAsync(new Domain.Entities.ExamCategory
            {
                Id = 2,
                Name = "Sports",
                Description = "Sport is a form of physical activity or game. Often competitive and organized, sports use, maintain, or " +
                "improve physical ability and skills.",
                DisplayOrder = 2,
                IsActive = true,
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "Sohada",
                ImageName = "02f1b308-2d5e-4a01-8f58-9dd2e3e60192.jpg"
            });


            await dataContext.ExamCategories.AddAsync(new Domain.Entities.ExamCategory
            {
                Id = 3,
                Name = "Public Knowledge",
                Description = "knowledge that is available to anyone.",
                DisplayOrder = 3,
                IsActive = false,
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "Sohada",
                ImageName = "02f1b308-2d5e-4a01-8f58-9dd2e3e60192.jpg"
            });

            await dataContext.Exams.AddAsync(new Domain.Entities.Exam
            {
                Id = new Guid("12345678-1234-1234-1234-123456789012"),
                Name = "Already in db",
                Description = "Already in db",
                Direction = Domain.Enums.ExamDirection.Shuffle,
                DurationInMinutes = 60,
                EndDate = new DateOnly(2025, 12, 31),
                ExamCategoryId = 1,
                HasReview = true,
                PassPercentage = 0.50M,
                StartDate = DateOnly.FromDateTime(DateTime.UtcNow),
                CreatedByUserId = "Sohada"
            });
            await dataContext.SaveChangesAsync();
        }
    }
}
