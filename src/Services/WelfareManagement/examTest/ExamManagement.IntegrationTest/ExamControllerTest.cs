﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Domain.Models;
using S3.Core.Test.Models;
using S3.ExamManagement.Api;
using S3.ExamManagement.Api.Exams.Dtos;
using S3.ExamManagement.Application.Exams.Commands.EditExam;
using S3.ExamManagement.Application.Exams.Queries.GetAllExamsByCategoryId;
using S3.ExamManagement.Application.Exams.Queries.GetExamDetails;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.IntegrationTest
{
    public class ExamControllerTest : VirtualWebServer<Program>
    {
        private ExamManagementDbContext _examManagementDbContext = null!;

        protected override async void SeedTestData()
        {
            ExamCategory[] examCategories =
            [
                new()
            {
                Id = 1,
                Name = "cat1",
                DisplayOrder = 1,
                IsActive = false,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now
            },
            new()
            {
                Id = 2,
                Name = "cat2",
                DisplayOrder = 2,
                IsActive = true,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now,
                Exams =
                {
                    new Domain.Entities.Exam
                    {
                        Id = Guid.NewGuid(),
                        Name = "Exam1",
                        Description = "test",
                        IsPublished = true,
                        ImageName = "test",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.Now),
                        PassPercentage = .50m,
                        DurationInMinutes = 100,
                        ExamCategoryId = 3,
                        CreatedByUserId = "1",
                        CreatedDate = DateTime.Now
                    }
                }
            },
            new()
            {
                Id = 3,
                Name = "cat3",
                DisplayOrder = 3,
                ImageName = "test",
                IsActive = true,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now,
                Exams = new List<Domain.Entities.Exam>
                {
                    new()
                    {
                        Id = Guid.NewGuid(),
                        Name = "Exam1",
                        Description = "test",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.Now),
                        PassPercentage = .50m,
                        DurationInMinutes = 100,
                        ExamCategoryId = 3,
                        CreatedByUserId = "1",
                        CreatedDate = DateTime.Now
                    }
                }
            }
            ];


            _examManagementDbContext.ExamCategories.AddRange(examCategories);
            Domain.Entities.Exam[] exams =
            [
                new()
            {
                CreatedByUserId = "1",
                DurationInMinutes = 60,
                EndDate = new DateOnly(2025, 3, 1),
                ExamCategoryId = 1,
                Id = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310"),
                ImageName = "r1.jpeg",
                IsPublished = true,
                Name = "Thyroid",
                PassPercentage = 0.50m,
                StartDate = new DateOnly(2025, 2, 25),
                ExamVersions = new List<ExamVersion>
                {
                    new()
                    {
                        Id = new Guid("776189ed-7cbb-4544-bf3e-641d7201eb3b"),
                        CreatedByUserId = "1",
                        TotalMarks = 100,
                        VersionNumber = "0.1"
                    },
                    new()
                    {
                        Id = new Guid("6c4345b5-f626-4bcf-aafa-a028a05f8ccd"),
                        CreatedByUserId = "1",
                        TotalMarks = 150,
                        VersionNumber = "0.2"
                    }
                }
            }
            ];


            _examManagementDbContext.Exams.AddRange(exams);
            await _examManagementDbContext.SaveChangesAsync();
        }


        protected override void OverrideServices(IServiceCollection services)
        {
            _examManagementDbContext = MockDataContext<ExamManagementDbContext>(services);

            MockService<IAuditService>(services);

            base.OverrideServices(services);
        }

        private static FormFile CreateImageFile()
        {
            // Create a random byte array representing a JPEG image
            var random = new Random();
            var imageBytes = new byte[1024]; // Size of the image in bytes (adjust as necessary)
            random.NextBytes(imageBytes); // Fill the byte array with random values

            var memoryStream = new MemoryStream(imageBytes);
            memoryStream.Position = 0; // Reset stream position to the beginning

            return new FormFile(memoryStream, 0L, imageBytes.Length, "formFile1", "test.jpg")
            {
                Headers = new HeaderDictionary(),
                ContentType = "image/jpeg" // Set correct content type for image
            };
        }

        #region EditExam API Test

        [Test]
        public async Task EditExam_ShouldReturnNotFound_WhenExamDoesNotExist()
        {
            // Arrange
            var command = new EditExamCommand
            {
                ExamId = Guid.NewGuid(), // Non-existent exam ID
                Name = "Updated Exam Name"
            };

            // Act
            var result =
                await PutFormAsync<EditExamCommand, Result<EditExamResult>>(@"Exams/Edit", command,
                    AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.NotFound));
            });
        }

        [Test]
        public async Task EditExam_ShouldPreventDurationChange_WhenExamHasVersions()
        {
            // Arrange
            var command = new EditExamCommand
            {
                ExamId = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310"), // Existing exam with versions
                DurationInMinutes = 120 // Trying to change the duration
            };

            // Act
            var result =
                await PutFormAsync<EditExamCommand, Result<EditExamResult>>(@"Exams/Edit", command,
                    AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Status, Is.EqualTo(ResultStatus.BadRequest));
            });
        }


        [Test]
        public async Task EditExam_ShouldUpdateExamSuccessfully_WhenValidDataProvided()
        {
            // Arrange
            var command = new EditExamCommand
            {
                ExamId = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310"), // Existing exam
                Name = "Updated Exam Name",
                Description = "Updated Description",

                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(1)
            };

            // Act
            var result =
                await PutFormAsync<EditExamCommand, Result<EditExamResult>>(@"Exams/Edit", command,
                    AppUtility.CreateCancelationToken());
            var updatedExam = await _examManagementDbContext.Exams.AsNoTracking()
                .FirstOrDefaultAsync(ex => ex.Id == command.ExamId);
            if (updatedExam != null)
                // Assert
                await Assert.MultipleAsync(async () =>
                {
                    Assert.That(result, Is.Not.Null);
                    Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));


                    Assert.That(updatedExam?.Name, Is.EqualTo(command.Name));
                    Assert.That(updatedExam?.Description, Is.EqualTo(command.Description));

                    Assert.That(updatedExam?.StartDate, Is.EqualTo(DateOnly.FromDateTime(command.StartDate.Value)));
                    Assert.That(updatedExam?.EndDate, Is.EqualTo(DateOnly.FromDateTime(command.EndDate.Value)));
                });
        }

        [Test]
        public async Task EditExam_ShouldSaveImage_WhenImageFileProvided()
        {
            // Arrange


            var command = new EditExamCommand
            {
                ExamId = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310"), // Existing exam
                ImageFile = CreateImageFile()
            };

            // Act
            var result =
                await PutFormAsync<EditExamCommand, Result<EditExamResult>>(@"Exams/Edit", command,
                    AppUtility.CreateCancelationToken());

            // Assert
            var updatedExam = await _examManagementDbContext.Exams.AsNoTracking()
                .FirstOrDefaultAsync(ex => ex.Id == command.ExamId);
            Assert.That(updatedExam?.ImageName, Is.Not.Null);
        }

        #endregion

        #region GetExamsByExamCategoryId API Test

        [Test]
        public async Task GetExamsByExamCategoryId_ReturnOnlyActiveExams_WithExamCategoriesExistAndActive()
        {
            // Arrange
            var requestDto = new GetAllExamsByExamCategoryIdRequestDto
            {
                ExamCategoryId = 2,
                PageNumber = 1,
                PageSize = 10
            };

            // Act
            var result = await GetAsync<Result<GetAllExamsByExamCategoryIdResult>>(
                @"Exams",
                requestDto,
                AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null, "Result should not be null.");
                Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
                Assert.That(result.Value, Is.Not.Null, "Returned data should not be null.");
            });
        }

        [Test]
        public async Task GetExamsByExamCategoryId_ReturnNotFound_WithExamCategoriesExistButNotActive()
        {
            // Arrange
            var requestDto = new GetAllExamsByExamCategoryIdRequestDto
            {
                ExamCategoryId = 1,
                PageNumber = 1,
                PageSize = 10
            };

            // Act
            var result = await GetAsync<Result<GetAllExamsByExamCategoryIdResult>>(
                @"Exams",
                requestDto,
                AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null, "Result should not be null.");
                Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound), "API response status should be OK.");
                Assert.That(result.Value, Is.Null, "Returned data should not be null.");
            });
        }

        [Test]
        public async Task GetExamsByExamCategoryId_ReturnNotFound_WithExamCategoriesNotExist()
        {
            // Arrange
            var requestDto = new GetAllExamsByExamCategoryIdRequestDto
            {
                ExamCategoryId = 5,
                PageNumber = 1,
                PageSize = 10
            };

            // Act
            var result = await GetAsync<Result<GetAllExamsByExamCategoryIdResult>>(
                @"Exams",
                requestDto,
                AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null, "Result should not be null.");
                Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound), "API response status should be OK.");
                Assert.That(result.Value, Is.Null, "Returned data should not be null.");
            });
        }

        #endregion

        #region GetExamVersionsByExamId

        [Test]
        public async Task GetExamDetails_ReturnOkAndIsSuccessTrue()
        {
            // Arrange 
            var getExamVersionsByExamIdDto = new GetExamDetailsDto
            {
                ExamId = new Guid("05526B0B-7E77-4190-B7CE-4E3B1E979310")
            };
            // Act
            var result = await GetAsync<Result<GetExamDetailsResult>>
                (@"Exams/getExamDetails", getExamVersionsByExamIdDto, AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
            });
        }

        [Test]
        public async Task GetExamDetails_ShouldReturnNotFound_WhenExamIdNotInDb()
        {
            // Arrange 
            var getExamVersionsByExamIdDto = new GetExamDetailsDto
            {
                ExamId = new Guid("776189ed-7cbb-4544-bf3e-641d7201eb3b")
            };
            // Act
            var result = await GetAsync<Result<GetExamDetailsResult>>
                (@"Exams/getExamDetails", getExamVersionsByExamIdDto, AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound));
            });
        }
        #endregion

        #region CreateExam API Test

        //[Test]
        //public async Task CreateExam_ReturnOkAndIsSuccessTrue()
        //{
        //    // Arrange 
        //    byte[] imageBytes = File.ReadAllBytes("D:\\Projects\\Exam\\src\\Services\\ExamManagement\\Tests\\ExamManagement.IntegrationTest\\ImageTest\\1.jpg");

        //    IFormFile mockFile = CreateMockFormFile(imageBytes, "2.jpg", "image/jpeg");
        //    var createExamDto = new CreateExamRequestDto
        //    {
        //        Name = "Pharmacology Exam",
        //        Description = "Pharmacology Exam",
        //        Image = mockFile,
        //        Direction = Domain.Enums.ExamDirection.Shuffle,
        //        DurationInMinutes = 60,
        //        EndDate = new DateOnly(2025, 12, 31),
        //        ExamCategoryId = 3,
        //        HasReview = true,
        //        PassPercentage = 0.50M,
        //        StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        //    };
        //    // Act
        //    var result = await PostFormAsync<CreateExamRequestDto, Result<CreateExamResponseDto>>
        //        (@"Exams/create", createExamDto);

        //    // Assert
        //    Assert.Multiple(() =>
        //    {
        //        Assert.That(result, Is.Not.Null);
        //        Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
        //    });
        //}

        //[Test]
        //public async Task CreateExam_CreateExamWithDuplicatedName_ReturnConflict()
        //{
        //    // Arrange 
        //    byte[] imageBytes = File.ReadAllBytes("D:\\Projects\\Exam\\src\\Services\\ExamManagement\\Tests\\ExamManagement.IntegrationTest\\ImageTest\\1.jpg");

        //    IFormFile mockFile = CreateMockFormFile(imageBytes, "2.jpg", "image/jpeg");
        //    var createExamDto = new CreateExamRequestDto
        //    {
        //        Name = "Exam1",
        //        Description = "Pharmacology Exam",
        //        Image = mockFile,
        //        Direction = Domain.Enums.ExamDirection.Shuffle,
        //        DurationInMinutes = 60,
        //        EndDate = new DateOnly(2025, 12, 31),
        //        ExamCategoryId = 3,
        //        HasReview = true,
        //        PassPercentage = 0.50M,
        //        StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        //    };
        //    // Act
        //    var result = await PostAsync<CreateExamRequestDto, Result<CreateExamResponseDto>>
        //        (@"Exams/create", createExamDto, AppUtility.CreateCancelationToken());

        //    // Assert
        //    Assert.That(result, Is.Not.Null);
        //    Assert.That(result!.Status, Is.EqualTo(ResultStatus.Conflict));
        //}

        //[Test]
        //public async Task CreateExam_CreateExamWithValidationError_ReturnBadRequest()
        //{
        //    //StartDateIsLessThanToday
        //    // Arrange 
        //    byte[] imageBytes = File.ReadAllBytes("D:\\Projects\\Exam\\src\\Services\\ExamManagement\\Tests\\ExamManagement.IntegrationTest\\ImageTest\\1.jpg");

        //    IFormFile mockFile = CreateMockFormFile(imageBytes, "2.jpg", "image/jpeg");
        //    var createExamDto = new CreateExamRequestDto
        //    {
        //        Name = "Pharmacology Exam 2",
        //        Description = "Pharmacology Exam",
        //        Image = mockFile,
        //        Direction = Domain.Enums.ExamDirection.Shuffle,
        //        DurationInMinutes = 60,
        //        EndDate = new DateOnly(2025, 12, 31),
        //        ExamCategoryId = 3,
        //        HasReview = true,
        //        PassPercentage = 0.50M,
        //        StartDate = DateOnly.FromDateTime(DateTime.UtcNow).AddDays(-1),
        //    };
        //    // Act
        //    var result = await PostAsync<CreateExamRequestDto, Result<CreateExamResponseDto>>
        //        (@"Exams/create", createExamDto, AppUtility.CreateCancelationToken());

        //    // Assert
        //    Assert.That(result, Is.Not.Null);
        //    Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest));
        //}

        //[Test]
        //public async Task CreateExam_InActiveCategory_ReturnConflict()
        //{
        //    // Arrange 
        //    byte[] imageBytes = File.ReadAllBytes("D:\\Projects\\Exam\\src\\Services\\ExamManagement\\Tests\\ExamManagement.IntegrationTest\\ImageTest\\1.jpg");

        //    IFormFile mockFile = CreateMockFormFile(imageBytes, "2.jpg", "image/jpeg");
        //    var createExamDto = new CreateExamRequestDto
        //    {
        //        Name = "Pharmacology Exam 3",
        //        Description = "Pharmacology Exam",
        //        Image = mockFile,
        //        Direction = Domain.Enums.ExamDirection.Shuffle,
        //        DurationInMinutes = 60,
        //        EndDate = new DateOnly(2025, 12, 31),
        //        ExamCategoryId = 1,
        //        HasReview = true,
        //        PassPercentage = 0.50M,
        //        StartDate = DateOnly.FromDateTime(DateTime.UtcNow)
        //    };
        //    // Act
        //    var result = await PostAsync<CreateExamRequestDto, Result<CreateExamResponseDto>>
        //        (@"Exams/create", createExamDto, AppUtility.CreateCancelationToken());

        //    // Assert
        //    Assert.That(result, Is.Not.Null);
        //    Assert.That(result!.Status, Is.EqualTo(ResultStatus.Conflict));
        //}

        #endregion
    }
}
