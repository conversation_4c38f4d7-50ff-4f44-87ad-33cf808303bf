﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <RootNamespace>S3.ExamManagement.IntegrationTest</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Exam.Core.Test" Version="0.0.3" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="NUnit" Version="4.2.2" />
    <PackageReference Include="NUnit.Analyzers" Version="4.4.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\ExamManagement.Api\ExamManagement.Api.csproj" />
    <ProjectReference Include="..\..\ExamManagement.Application\ExamManagement.Application.csproj" />
    <ProjectReference Include="..\..\ExamManagement.Domain\ExamManagement.Domain.csproj" />
    <ProjectReference Include="..\..\ExamManagement.Infrastructures\ExamManagement.Infrastructures.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="NUnit.Framework" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="ImageTest\" />
  </ItemGroup>

</Project>
