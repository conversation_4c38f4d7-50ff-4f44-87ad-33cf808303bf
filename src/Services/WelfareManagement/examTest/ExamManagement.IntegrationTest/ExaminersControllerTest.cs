﻿using Microsoft.Extensions.DependencyInjection;
using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.Core.Domain.Enums;
using S3.Core.Domain.Models;
using S3.Core.Test.Models;
using S3.ExamManagement.Api;
using S3.ExamManagement.Application.Examiners.Commands.RegisterExaminer;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.IntegrationTest;

[TestFixture]
public class RegisterExaminerIntegrationTest : VirtualWebServer<Program>
{
    private ExamManagementDbContext _examManagementDbContext = null!;

    protected override async void SeedTestData()
    {
        var examiners = new List<Examiner>
        {
            new()
            {
                Email = "<EMAIL>",
                DisplayName = "Existing Examiner",
                PhoneNumber = "1234567890",
                CreatedByUserId = "1",
                CreatedDate = DateTime.UtcNow
            }
        };

        _examManagementDbContext.Examiners.AddRange(examiners);
        await _examManagementDbContext.SaveChangesAsync();
    }

    protected override void OverrideServices(IServiceCollection services)
    {
        _examManagementDbContext = MockDataContext<ExamManagementDbContext>(services);
        MockService<IAuditService>(services);
        base.OverrideServices(services);
    }

    [Test]
    public async Task RegisterExaminer_ShouldReturnOk_WhenExaminerIsNew()
    {
        // Arrange
        var registerExaminerCommand = new RegisterExaminerCommand
        {
            DisplayName = "New Examiner",
            Email = "<EMAIL>",
            PhoneNumber = "9876543210",
            Header = new RequestHeader
            {
                UserId = "TestUser",
                UserName = "user",
                Roles = null
            }
        };

        // Act
        var result = await PostAsync<RegisterExaminerCommand, Result<RegisterExaminerResult>>(
            @"examiners/register", registerExaminerCommand, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Status, Is.EqualTo(ResultStatus.Ok));
        });
    }

    [Test]
    public async Task RegisterExaminer_ShouldReturnOK_WhenEmailAlreadyExists()
    {
        // Arrange
        var registerExaminerCommand = new RegisterExaminerCommand
        {
            DisplayName = "Duplicate Examiner",
            Email = "<EMAIL>",
            PhoneNumber = "1112223333",
            Header = new RequestHeader
            {
                UserId = "TestUser",
                UserName = "user",
                Roles = null
            }
        };

        // Act
        var result = await PostAsync<RegisterExaminerCommand, Result<RegisterExaminerResult>>(
            @"examiners/register", registerExaminerCommand, AppUtility.CreateCancelationToken());

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
    }
}