﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Domain.Models;
using S3.Core.Test.Models;
using S3.ExamManagement.Api;
using S3.ExamManagement.Api.ExamCategories.Dtos;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.IntegrationTest;

public class ExamCategoryControllerTest : VirtualWebServer<Program>
{
    private ExamManagementDbContext _examManagementDbContext = null!;

    protected override async void SeedTestData()
    {
        ExamCategory[] examCategories =
        [
            new ExamCategory()
            {
                Id = 1,
                Name = "cat1",
                DisplayOrder = 1,
                IsActive = false,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now
            },
            new ExamCategory()
            {
                Id = 2,
                Name = "cat2",
                DisplayOrder = 2,
                IsActive = true,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now,
                Exams =new List<Domain.Entities.Exam>()
                {
                    new()
                    {
                        Id = Guid.NewGuid(),
                        Name = "Exam1",
                        Description = "test",
                        IsPublished = true,
                        ImageName = "test",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.Now),
                        PassPercentage = .50m,
                        DurationInMinutes = 100,
                        ExamCategoryId = 3,
                        CreatedByUserId = "1",
                        CreatedDate = DateTime.Now
                    }
                }
            },
            new ExamCategory()
            {
                Id = 3,
                Name = "cat3",
                DisplayOrder = 3,
                ImageName = "test",
                IsActive = true,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now,
                Exams = new List<Domain.Entities.Exam>
                {
                    new()
                    {
                        Id = Guid.NewGuid(),
                        Name = "Exam1",
                        Description = "test",
                        StartDate = DateOnly.FromDateTime(DateTime.Now),
                        EndDate = DateOnly.FromDateTime(DateTime.Now),
                        PassPercentage = .50m,
                        DurationInMinutes = 100,
                        ExamCategoryId = 3,
                        CreatedByUserId = "1",
                        CreatedDate = DateTime.Now
                    }
                }
            },
            new()
            {
                Id = 4,
                Name = "cat3",
                DisplayOrder = 3,
                ImageName = "test",
                IsActive = true,
                CreatedByUserId = "1",
                CreatedDate = DateTime.Now,
                Exams = null

            }
        ];


        _examManagementDbContext.ExamCategories.AddRange(examCategories);
        await _examManagementDbContext.SaveChangesAsync();
    }


    protected override void OverrideServices(IServiceCollection services)
    {
        _examManagementDbContext = MockDataContext<ExamManagementDbContext>(services);
        MockService<IAuditService>(services);

        base.OverrideServices(services);
    }
    private IFormFile CreateImageFile(string fileName = "test-image.jpeg", string contentType = "image/jpeg")
    {
        // Generate a small in-memory byte array representing an image
        var imageBytes = new byte[]
        {
            0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01 // JPEG header bytes (example)
        };

        var stream = new MemoryStream(imageBytes);
        stream.Position = 0; // Reset stream position

        return new FormFile(stream, 0, imageBytes.Length, "formFile", fileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = contentType // Ensure this matches the type of image, e.g., "image/jpeg"
        };
    }

    #region GetExamCategoriesWithExams API Test

    [Test]
    public async Task GetAllExamCategories_ReturnOnlyActiveExamCategoriesWithActiveExams()
    {
        // Arrange
        var requestDto = new GetExamCategoriesWithExamsRequestDto
        {
            ItemPerPage = 4,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await GetAsync<Result<GetExamCategoriesWithExamsResponseDto>>(
            @"ExamCategories/GetExamCategoriesWithExams",
            requestDto,
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
            Assert.That(result.Value, Is.Not.Null, "Returned data should not be null.");
        });
    }

    #endregion

    #region ActivateDeactivateExamCategory API Test

    [Test]
    public async Task ActivateDeactivateExamCategory_ReturnOkAndIsSuccessTrue()
    {
        // Arrange   
        var requestDto = new ActivateDeactivateExamCategoryRequestDto
        {
            ExamCategoryId = 2
        };


        // Act
        var result =
            await
                PostAsync<ActivateDeactivateExamCategoryRequestDto, Result<ActivateDeactivateExamCategoryResponseDto>>(
                    @"ExamCategories/activateDeactivate", requestDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
            Assert.That(result.Value, Is.Not.Null);
        });
    }

    [Test]
    public async Task ActivateDeactivateExamCategory_ByInvalidExamCategoryId_ReturnNotFound()
    {
        // Arrange   
        var requestDto = new ActivateDeactivateExamCategoryRequestDto
        {
            ExamCategoryId = 5
        };


        // Act
        var result =
            await
                PostAsync<ActivateDeactivateExamCategoryRequestDto, Result<ActivateDeactivateExamCategoryResponseDto>>(
                    @"ExamCategories/activateDeactivate", requestDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound));
            Assert.That(result.Value, Is.Null);
        });
    }

    #endregion

    #region DeleteExamCategory API Test

    [Test]
    public async Task DeleteExamCategory_ReturnBadRequestWhenHasExams()
    {
        // Act
        var result =
            await DeleteAsync<Result<DeleteExamCategoryResponseDto>>($@"ExamCategories/{2}",
                AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Status, Is.EqualTo(ResultStatus.BadRequest));
        });
    }
    [Test]
    public async Task DeleteExamCategory_DeleteByExamCategoryId_ReturnOkAndIsSuccessTrue()
    {
        // Act
        var result =
            await DeleteAsync<Result<DeleteExamCategoryResponseDto>>($@"ExamCategories/{4}",
                AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value!.IsSuccess, Is.EqualTo(true));
            Assert.That(result.Status, Is.EqualTo(ResultStatus.Ok));
        });
    }

    [Test]
    public async Task DeleteExamCategory_DeleteByInvalidExamCategoryId_ReturnNotFound()
    {
        // Act
        var result =
            await DeleteAsync<Result<DeleteExamCategoryResponseDto>>($@"ExamCategories/{6}",
                AppUtility.CreateCancelationToken());

        // Assert
        Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound));
    }

    [Test]
    public async Task DeleteExamCategory_DeleteExamCategoryThatHasExams_ReturnBadRequest()
    {
        // Act
        var result =
            await DeleteAsync<Result<DeleteExamCategoryResponseDto>>($@"ExamCategories/{3}",
                AppUtility.CreateCancelationToken());

        // Assert
        Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest));
    }

    #endregion

    #region CreateExamCategory API Test

    [Test]
    public async Task CreateExamCategory_ReturnOkAndIsSuccessTrue()
    {
        // Arrange 
        var createExamCategoryDto = new CreateExamCategoryRequestDto
        {
            Name = "a",
            Description = "a",
            ImageFile = null
        };
        // Act
        var result = await PostFormAsync<CreateExamCategoryRequestDto, Result<CreateExamCategoryResponseDto>>
            (@"ExamCategories/create", createExamCategoryDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
        });
    }

    [Test]
    public async Task CreateExamCategory_CreateExamCategoryWithDuplicatedName_ReturnConflict()
    {
        // Arrange 
        var createExamCategoryDto = new CreateExamCategoryRequestDto
        {
            Name = "cat1",
            Description = "a",
            ImageFile = null
        };
        // Act
        var result = await PostFormAsync<CreateExamCategoryRequestDto, Result<CreateExamCategoryResponseDto>>
            (@"ExamCategories/create", createExamCategoryDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Status, Is.EqualTo(ResultStatus.Conflict));
    }

    [Test]
    public async Task CreateExamCategory_CreateExamCategoryWithValidationError_ReturnBadRequest()
    {
        // Arrange 
        var createExamCategoryDto = new CreateExamCategoryRequestDto
        {
            Name = "",
            Description = "a",
            ImageFile = null
        };
        // Act
        var result = await PostFormAsync<CreateExamCategoryRequestDto, Result<CreateExamCategoryResponseDto>>
            (@"ExamCategories/create", createExamCategoryDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest));
    }

    #endregion

    #region EditExamCategory API Test

    [Test]
    public async Task EditExamCategory_ReturnOkAndIsSuccessTrue()
    {
        // Arrange 
        var editExamCategoryDto = new EditExamCategoryDto
        {
            Name = "Biology",
            Description =
                "Biology is the overall natural science that studies life, with the other life sciences as its " +
                "sub-disciplines.",
            Image = null,
            Id = 1
        };
        // Act
        var result = await PutFormAsync<EditExamCategoryDto, Result<Application.ExamCategories.Commands.EditExamCategories.EditExamCategoryResult>>
            (@"ExamCategories/update", editExamCategoryDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
        });
    }

    [Test]
    public async Task EditExamCategory_EditExamCategoryWithDuplicatedName_ReturnConflict()
    {
        // Arrange 
        var editExamCategoryDto = new EditExamCategoryDto
        {
            Id = 2,
            Name = "cat1",
            Description = "some description",
            Image = null
        };
        // Act
        var result = await PutFormAsync<EditExamCategoryDto, Result<EditExamCategoryResponseDto>>
            (@"ExamCategories/update", editExamCategoryDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Status, Is.EqualTo(ResultStatus.Conflict));
    }

    [Test]
    public async Task EditExamCategory_EditExamCategoryWithValidationError_ReturnBadRequest()
    {
        // Arrange 
        var editExamCategoryDto = new EditExamCategoryDto
        {
            Id = -1,
            Name = "hjhgjg",
            Description = "some description",
            Image = null
        };
        // Act
        var result = await PutFormAsync<EditExamCategoryDto, Result<EditExamCategoryResponseDto>>
            (@"ExamCategories/update", editExamCategoryDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest));
    }

    [Test]
    public async Task EditExamCategory_EditExamCategoryWithAnIdThatDoesNotExist_ReturnNotFound()
    {
        // Arrange 
        var editExamCategoryDto = new EditExamCategoryDto
        {
            Id = 5,
            Name = "new cat",
            Description = "new description",
            Image = null
        };
        // Act
        var result = await PutFormAsync<EditExamCategoryDto, Result<EditExamCategoryResponseDto>>
            (@"ExamCategories/update", editExamCategoryDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound));
        //Assert.That(result!.ErrorMessage, Is.EqualTo(Resources.CategoryNotFound));
    }

    #endregion
}