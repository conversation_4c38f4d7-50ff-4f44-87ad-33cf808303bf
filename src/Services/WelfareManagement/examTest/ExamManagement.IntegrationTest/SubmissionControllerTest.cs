﻿using Microsoft.Extensions.DependencyInjection;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Domain.Models;
using S3.Core.Test.Models;
using S3.ExamManagement.Api;
using S3.ExamManagement.Api.Submissions.Dtos;
using S3.ExamManagement.Application.Services.Audit;
using S3.ExamManagement.Domain.Entities;
using S3.ExamManagement.Domain.Enums;
using S3.ExamManagement.Infrastructures.Services.Persistence;

namespace S3.ExamManagement.IntegrationTest;

public class SubmissionControllerTest : VirtualWebServer<Program>
{
    private ExamManagementDbContext _examManagementDbContext = null!;

    protected override async void SeedTestData()
    {
        var submission = new Submission
        {
            Id = 1,
            ExaminerId = 1,
            ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
            ScorePercentage = 85,
            ExamVersion = new ExamVersion
            {
                Id = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
                VersionNumber = "1.0",
                TotalMarks = 100,
                Status = ExamVersionStatus.Active,
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "admin",
                ExamVersionResultFormats = new List<ExamVersionResultFormat>
                {
                    new()
                    {
                        ResultFormat = new ResultFormat { Id = 1, NameEn = "grade", NameAr = "" },

                        GradeRanges = new List<GradeRange>
                        {
                            new() { MinMark = 80, MaxMark = 100, Grade = "A" }
                        }
                    },
                    new()
                    {
                        ResultFormat = new ResultFormat { Id = 2, NameEn = "percentage", NameAr = "" }
                    }
                },
                Exam = new()
                {
                    Id = Guid.CreateVersion7(),
                    Name = "test1",
                    PassPercentage = 0.5m,
                    CreatedByUserId = "11111",
                    CreatedDate = DateTime.UtcNow
                }
            }
        };
        _examManagementDbContext.Submissions.AddRange(submission);
        await _examManagementDbContext.SaveChangesAsync();
    }


    protected override void OverrideServices(IServiceCollection services)
    {
        _examManagementDbContext = MockDataContext<ExamManagementDbContext>(services);
        MockService<IAuditService>(services);

        base.OverrideServices(services);
    }

    #region GetSubmissionResult API Test

    [Test]
    public async Task GetSubmissionResult_ReturnOk_WhenSubmissionExists()
    {
        // Arrange 
        const int submissionId = 1;

        // Act
        var result = await GetAsync<Result<GetSubmissionReviewResponseDto>>
            ($@"Submissions/{submissionId}", AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Value, Is.Not.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
        });
    }

    [Test]
    public async Task GetSubmissionResult_ReturnNotFound_WhenSubmissionNotExists()
    {
        // Arrange 
        const int submissionId = 10;

        // Act
        var result = await GetAsync<Result<GetSubmissionReviewResponseDto>>
            ($@"Submissions/{submissionId}", AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Value, Is.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound));
        });
    }

    #endregion

    #region GetExamVersionResult API Test

    [Test]
    public async Task GetExamVersionResult_ReturnOk()
    {
        // Arrange 
        const int submissionId = 1;

        // Act
        var result = await GetAsync<Result<GetExamVersionResultResponseDto>>
            ($@"Submissions/{submissionId}", AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Value, Is.Not.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
        });
    }

    [Test]
    public async Task GetExamVersionResult_ReturnNotFound()
    {
        // Arrange 
        const int submissionId = 9;

        // Act
        var result = await GetAsync<Result<GetExamVersionResultResponseDto>>
            ($@"Submissions/{submissionId}", AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Value, Is.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound));
        });
    }
    //
    // [Test]
    // public async Task CreateSubmission_DuplicateSubmission_ReturnConflict()
    // {
    //     // Arrange 
    //     var createSubmissionDto = new CreateSubmissionRequestDto
    //     {
    //         ExaminerId = 1,
    //         ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
    //         Questions = new List<SubmissionAnswerItemDto> { new() { AnswerIds = [1], QuestionId = 1 } }
    //     };
    //     // Act
    //     var result = await PostAsync<CreateSubmissionRequestDto, Result<CreateSubmissionResponseDto>>
    //         (@"Submissions/create", createSubmissionDto, AppUtility.CreateCancelationToken());
    //
    //     // Assert
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(result, Is.Not.Null);
    //         Assert.That(result!.Status, Is.EqualTo(ResultStatus.Conflict));
    //     });
    // }
    //
    // [Test]
    // public async Task CreateSubmission_ValidationError_ReturnBadRequest()
    // {
    //     // Arrange 
    //     var createSubmissionDto = new CreateSubmissionRequestDto
    //     {
    //         ExaminerId = 1,
    //         ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
    //         StartDateTime = DateTime.Now,
    //         FinishDateTime = DateTime.Now.AddDays(-1),
    //         Questions = new List<SubmissionAnswerItemDto> { new() { AnswerIds = [1], QuestionId = 1 } }
    //     };
    //     // Act
    //     var result = await PostAsync<CreateSubmissionRequestDto, Result<CreateSubmissionResponseDto>>
    //         (@"Submissions/create", createSubmissionDto, AppUtility.CreateCancelationToken());
    //
    //     // Assert
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(result, Is.Not.Null);
    //         Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest));
    //     });
    // }

    #endregion

    #region CreateSubmission API Test

    [Test]
    public async Task CreateSubmission_ReturnOkAndIsSuccessTrue()
    {
        // Arrange 
        var createSubmissionDto = new CreateSubmissionRequestDto
        {
            ExaminerId = 1,
            ExamVersionId = Guid.Parse("b6ed261a-bc17-40e9-b0cc-6c54d1dfd6d2"),
            Questions = new List<SubmissionAnswerItemDto> { new() { AnswerIds = [1], QuestionId = 1 } }
        };
        // Act
        var result = await PostAsync<CreateSubmissionRequestDto, Result<CreateSubmissionResponseDto>>
            (@"Submissions/create", createSubmissionDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok));
        });
    }


    [Test]
    public async Task CreateSubmission_ValidationError_ReturnBadRequest()
    {
        // Arrange 
        var createSubmissionDto = new CreateSubmissionRequestDto
        {
            ExaminerId = 1,
            ExamVersionId = Guid.Parse("4de396c8-c2ef-4ab3-8cb6-0b8226d0889f"),
            StartDateTime = DateTime.Now,
            FinishDateTime = DateTime.Now.AddDays(-1),
            Questions = new List<SubmissionAnswerItemDto> { new() { AnswerIds = [1], QuestionId = 1 } }
        };
        // Act
        var result = await PostAsync<CreateSubmissionRequestDto, Result<CreateSubmissionResponseDto>>
            (@"Submissions/create", createSubmissionDto, AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest));
        });
    }

    #endregion
}