﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Domain.Models;
using S3.Core.Test.Models;
using S3.ExamManagement.Api;
using S3.ExamManagement.Api.Accounts.Dtos;

namespace S3.ExamManagement.IntegrationTest
{
    internal class AccountControllerTest : VirtualWebServer<Program>
    {
        protected override void SeedTestData()
        {

        }

        #region AdminLogin Api Test
        [Test]
        public async Task AdminLogin_ReturnOk()
        {
            // Arrange
            var requestDto = new AdminLoginRequestDto
            {
                UserName = "Admin",
                Password = "P@ssw0rd"
            };

            // Act
            var result = await GetAsync<Result<AdminLoginResponseDto>>(@"Accounts/AdminLogin", requestDto, AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null, "Result should not be null.");
                Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
                Assert.That(result.Value?.IsAuthenticated, Is.EqualTo(true), "IsAuthenticated should be true.");
            });
        }

        [Test]
        public async Task AdminLogin_WrongUserName_ReturnBadRequest()
        {
            // Arrange
            var requestDto = new AdminLoginRequestDto
            {
                UserName = "Chase",
                Password = "P@ssw0rd"
            };

            // Act
            var result = await GetAsync<Result<AdminLoginResponseDto>>(@"Accounts/AdminLogin", requestDto, AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null, "Result should not be null.");
                Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest), "API response status should be BadRequest.");
            });
        }

        [Test]
        public async Task AdminLogin_WrongPassword_ReturnBadRequest()
        {
            // Arrange
            var requestDto = new AdminLoginRequestDto
            {
                UserName = "admin",
                Password = "P@f"
            };

            // Act
            var result = await GetAsync<Result<AdminLoginResponseDto>>(@"Accounts/AdminLogin", requestDto, AppUtility.CreateCancelationToken());

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(result, Is.Not.Null, "Result should not be null.");
                Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest), "API response status should be BadRequest.");
            });
        }
        #endregion
    }
}
