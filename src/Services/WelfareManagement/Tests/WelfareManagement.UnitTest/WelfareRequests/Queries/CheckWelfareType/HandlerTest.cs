using Microsoft.Extensions.Logging;
using Moq;
using S3.Core.Application.Enums;
using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.Core.Test.Models;
using S3.MoL.WelfareManagement.Application.Services.Persistence;
using S3.MoL.WelfareManagement.Application.WelfareRequests.Queries.CheckWelfareType;
using S3.MoL.WelfareManagement.Domain.Entities;
using S3.MoL.WelfareManagement.Domain.Enums;
using S3.MoL.WelfareManagement.Infrastructure.Services.Persistence;
using static NUnit.Framework.Assert;
using Handler = S3.MoL.WelfareManagement.Application.WelfareRequests.Queries.CheckWelfareType.Handler;

namespace S3.MoL.WelfareManagement.UnitTest.WelfareRequests.Queries.CheckWelfareType;

internal class HandlerTest
{
    private WelfareManagementDbContext _context = default!;
    private Handler _handler = default!;
    private Mock<ILogger<Handler>> _logger = default!;

    [SetUp]
    public async Task SetUp()
    {
        _context = TestUtility.CreateDbContext<WelfareManagementDbContext>();
        await SeedDbContext(_context);

        _logger = new Mock<ILogger<Handler>>();
        _handler = new Handler(_context, _logger.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _context.Dispose();
    }

    #region SeedData

    private static async Task SeedDbContext(IWelfareManagementDbContext dataContext)
    {
        // Seed Occupations
        var occupations = new List<Occupation>
        {
            new()
            {
                OccupationId = 1,
                Code = "OCC001",
                Text = "Engineer",
                IsActive = true,
                IsDeleted = false
            }
        };
        await dataContext.Occupations.AddRangeAsync(occupations);

        // Seed WelfareCategories
        var welfareCategories = new List<WelfareCategory>
        {
            new()
            {
                WelfareCategoryId = 1,
                Code = "WC001",
                Text = "Social Welfare"
            }
        };
        await dataContext.WelfareCategories.AddRangeAsync(welfareCategories);

        // Seed WelfareTypes
        var welfareTypes = new List<WelfareType>
        {
            new()
            {
                WelfareTypeId = (int)WelfareTypes.Marriage,
                Code = "WT-M_01",
                Text = "Marriage",
                WelfareCategoryId = 1,
                MaximumLimit = 1
            },
            new()
            {
                WelfareTypeId = (int)WelfareTypes.NewBorn,
                Code = "WT-M_02",
                Text = "Newborn",
                WelfareCategoryId = 1,
                MaximumLimit = 2
            },
            new()
            {
                WelfareTypeId = 99,
                Code = "WT-M_99",
                Text = "No Limit Type",
                WelfareCategoryId = 1,
                MaximumLimit = null
            }
        };
        await dataContext.WelfareTypes.AddRangeAsync(welfareTypes);

        // Seed Labors
        var labors = new List<Labor>
        {
            new()
            {
                LaborId = 1,
                NationalId = "12345678901234",
                FullName = "John Doe",
                MobileNo = "1234567890123",
                OccupationId = 1,
                IsBeneficiary = true
            },
            new()
            {
                LaborId = 2,
                NationalId = "12345678901235",
                FullName = "Jane Smith",
                MobileNo = "1234567890124",
                OccupationId = 1,
                IsBeneficiary = false
            }
        };
        await dataContext.Labors.AddRangeAsync(labors);

        // Seed RequestStatuses
        var requestStatuses = new List<RequestStatus>
        {
            new()
            {
                RequestStatusId = (int)RequestStatuses.InProgress,
                Code = "InProgress",
                Text = "InProgress"
            },
            new()
            {
                RequestStatusId = (int)RequestStatuses.Completed,
                Code = "Completed",
                Text = "Completed"
            }
        };
        await dataContext.RequestStatuses.AddRangeAsync(requestStatuses);

        // Seed Directorates (required for WelfareRequest)
        var directorates = new List<Directorate>
        {
            new()
            {
                DirectorateId = 1,
                Code = "DIR001",
                Text = "Test Directorate",
                IsActive = true,
                IsDeleted = false,
                GovernorateId = 1
            }
        };
        await dataContext.Directorates.AddRangeAsync(directorates);

        await dataContext.SaveChangesAsync();
    }

    #endregion

    private CheckWelfareTypeQuery CreateQuery(long laborId, int welfareTypeId)
    {
        return new CheckWelfareTypeQuery
        {
            LaborId = laborId,
            WelfareTypeId = welfareTypeId,
            Header = new RequestHeader
            {
                UserId = "user",
                UserName = "test",
                Roles = null,
                OrganizationUnitId = 1
            }
        };
    }

    [Test]
    public async Task Handle_ShouldReturnNotFound_WhenLaborDoesNotExist()
    {
        // Arrange
        var query = CreateQuery(999, (int)WelfareTypes.Marriage);

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.NotFound), "Result status should be NotFound.");
            That(result.Target, Is.EqualTo(nameof(query.LaborId)), "Target should be LaborId.");
        });
    }

    [Test]
    public async Task Handle_ShouldReturnNotFound_WhenWelfareTypeDoesNotExist()
    {
        // Arrange
        var query = CreateQuery(1, 999);

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.NotFound), "Result status should be NotFound.");
            That(result.Target, Is.EqualTo(nameof(query.WelfareTypeId)), "Target should be WelfareTypeId.");
        });
    }

    [Test]
    public async Task Handle_ShouldReturnForbidden_WhenLaborIsNotBeneficiary()
    {
        // Arrange
        var query = CreateQuery(2, (int)WelfareTypes.Marriage); // Labor 2 is not a beneficiary

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Forbidden), "Result status should be Forbidden.");
            That(result.Target, Is.EqualTo("labor"), "Target should be labor.");
        });
    }

    [Test]
    public async Task Handle_ShouldReturnOk_WithEligibleTrue_WhenAllConditionsAreMet()
    {
        // Arrange
        var query = CreateQuery(1, (int)WelfareTypes.Marriage);

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Ok), "Result status should be Ok.");
            That(result.Value, Is.Not.Null, "Result value should not be null.");
            That(result.Value!.IsBeneficairy, Is.True, "Should be eligible for welfare.");
        });
    }

    [Test]
    public async Task Handle_ShouldReturnOk_WithEligibleTrue_WhenWelfareTypeHasNoMaximumLimit()
    {
        // Arrange
        var query = CreateQuery(1, 99); // WelfareType with no maximum limit

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Ok), "Result status should be Ok.");
            That(result.Value, Is.Not.Null, "Result value should not be null.");
            That(result.Value!.IsBeneficairy, Is.True, "Should be eligible when no maximum limit.");
        });
    }

    [Test]
    public async Task Handle_ShouldReturnConflict_WhenInProgressRequestExists()
    {
        // Arrange
        var query = CreateQuery(1, (int)WelfareTypes.Marriage);

        // Add an in-progress welfare request
        await SeedInProgressWelfareRequest();

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Conflict), "Result status should be Conflict.");
            That(result.Target, Is.EqualTo("WelfareRequest"), "Target should be WelfareRequest.");
        });
    }

    [Test]
    public async Task Handle_ShouldReturnOk_WithEligibleFalse_WhenGeneralEligibilityLimitExceeded()
    {
        // Arrange
        var query = CreateQuery(1, (int)WelfareTypes.Marriage); // Marriage has MaximumLimit = 1

        // Add a completed welfare request to exceed the limit
        await SeedCompletedWelfareRequest();

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Ok), "Result status should be Ok.");
            That(result.Value, Is.Not.Null, "Result value should not be null.");
            That(result.Value!.IsBeneficairy, Is.False, "Should not be eligible when limit exceeded.");
        });
    }

    [Test]
    public async Task Handle_ShouldReturnOk_WithEligibleTrue_ForNewbornWithinLimit()
    {
        // Arrange
        var query = CreateQuery(1, (int)WelfareTypes.NewBorn); // NewBorn has MaximumLimit = 2

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Ok), "Result status should be Ok.");
            That(result.Value, Is.Not.Null, "Result value should not be null.");
            That(result.Value!.IsBeneficairy, Is.True, "Should be eligible for newborn within limit.");
        });
    }

    [Test]
    public async Task Handle_ShouldReturnOk_WithEligibleFalse_ForNewbornWhenLimitExceededWithTwins()
    {
        // Arrange
        var query = CreateQuery(1, (int)WelfareTypes.NewBorn); // NewBorn has MaximumLimit = 2

        // Add completed newborn requests including twins to exceed limit
        await SeedCompletedNewbornRequestsWithTwins();

        // Act
        var result = await _handler.Handle(query, AppUtility.CreateCancelationToken());

        // Assert
        Multiple(() =>
        {
            That(result.Status, Is.EqualTo(ResultStatus.Ok), "Result status should be Ok.");
            That(result.Value, Is.Not.Null, "Result value should not be null.");
            That(result.Value!.IsBeneficairy, Is.False,
                "Should not be eligible when newborn limit exceeded with twins.");
        });
    }

    #region Helper Methods

    private async Task SeedInProgressWelfareRequest()
    {
        var welfareRequest = new SocialWelfareRequest
        {
            LaborId = 1,
            RequestStatusId = (int)RequestStatuses.InProgress,
            RequestNo = "REQ001",
            DirectorateId = 1,
            EventDate = DateOnly.FromDateTime(DateTime.Now),
            NationalId = "12345678901234",
            CreatedDate = DateTime.UtcNow,
            CreatedByUserId = "test",
            CreatedByUserName = "test",
            Version = new byte[8]
        };

        // Use reflection to set the protected WelfareTypeId property
        var welfareTypeIdProperty = typeof(WelfareRequest).GetProperty(nameof(WelfareRequest.WelfareTypeId));
        welfareTypeIdProperty?.SetValue(welfareRequest, (int)WelfareTypes.Marriage);

        _context.SocialWelfareRequests.Add(welfareRequest);
        await _context.SaveChangesAsync();
    }

    private async Task SeedCompletedWelfareRequest()
    {
        var welfareRequest = new SocialWelfareRequest
        {
            LaborId = 1,
            RequestStatusId = (int)RequestStatuses.Completed,
            RequestNo = "REQ002",
            DirectorateId = 1,
            EventDate = DateOnly.FromDateTime(DateTime.Now),
            NationalId = "12345678901234",
            CreatedDate = DateTime.UtcNow,
            CreatedByUserId = "test",
            CreatedByUserName = "test",
            Version = new byte[8]
        };

        // Use reflection to set the protected WelfareTypeId property
        var welfareTypeIdProperty = typeof(WelfareRequest).GetProperty(nameof(WelfareRequest.WelfareTypeId));
        welfareTypeIdProperty?.SetValue(welfareRequest, (int)WelfareTypes.Marriage);

        _context.SocialWelfareRequests.Add(welfareRequest);
        await _context.SaveChangesAsync();
    }

    private async Task SeedCompletedNewbornRequestsWithTwins()
    {
        var request1 = new SocialWelfareRequest
        {
            LaborId = 1,
            RequestStatusId = (int)RequestStatuses.Completed,
            RequestNo = "REQ003",
            DirectorateId = 1,
            EventDate = DateOnly.FromDateTime(DateTime.Now),
            NationalId = "12345678901234",
            IsTwin = false,
            CreatedDate = DateTime.UtcNow,
            CreatedByUserId = "test",
            CreatedByUserName = "test",
            Version = new byte[8]
        };

        var request2 = new SocialWelfareRequest
        {
            LaborId = 1,
            RequestStatusId = (int)RequestStatuses.Completed,
            RequestNo = "REQ004",
            DirectorateId = 1,
            EventDate = DateOnly.FromDateTime(DateTime.Now),
            NationalId = "12345678901234",
            IsTwin = true, // This counts as 2 slots
            CreatedDate = DateTime.UtcNow,
            CreatedByUserId = "test",
            CreatedByUserName = "test",
            Version = new byte[8]
        };

        // Use reflection to set the protected WelfareTypeId property
        var welfareTypeIdProperty = typeof(WelfareRequest).GetProperty(nameof(WelfareRequest.WelfareTypeId));
        welfareTypeIdProperty?.SetValue(request1, (int)WelfareTypes.NewBorn);
        welfareTypeIdProperty?.SetValue(request2, (int)WelfareTypes.NewBorn);

        _context.SocialWelfareRequests.AddRange(request1, request2);
        await _context.SaveChangesAsync();
    }

    #endregion
}