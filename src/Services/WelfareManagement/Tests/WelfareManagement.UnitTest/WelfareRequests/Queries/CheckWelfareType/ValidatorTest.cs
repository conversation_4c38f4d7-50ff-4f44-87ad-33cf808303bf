using FluentValidation.TestHelper;
using S3.MoL.WelfareManagement.Application.WelfareRequests.Queries.CheckWelfareType;

namespace S3.MoL.WelfareManagement.UnitTest.WelfareRequests.Queries.CheckWelfareType;

internal class ValidatorTest
{
    private CheckWelfareTypeValidator _validator = default!;

    [SetUp]
    public void SetUp()
    {
        _validator = new CheckWelfareTypeValidator();
    }

    [Test]
    public void Validate_ShouldHaveError_WhenLaborIdIsEmpty()
    {
        // Arrange
        var query = new CheckWelfareTypeQuery { LaborId = 0, WelfareTypeId = 1 };

        // Act & Assert
        var result = _validator.TestValidate(query);
        result.ShouldHaveValidationErrorFor(x => x.LaborId);
    }

    [Test]
    public void Validate_ShouldHaveError_WhenLaborIdIsNegative()
    {
        // Arrange
        var query = new CheckWelfareTypeQuery { LaborId = -1, WelfareTypeId = 1 };

        // Act & Assert
        var result = _validator.TestValidate(query);
        result.ShouldHaveValidationErrorFor(x => x.LaborId);
    }

    [Test]
    public void Validate_ShouldHaveError_WhenWelfareTypeIdIsEmpty()
    {
        // Arrange
        var query = new CheckWelfareTypeQuery { LaborId = 1, WelfareTypeId = 0 };

        // Act & Assert
        var result = _validator.TestValidate(query);
        result.ShouldHaveValidationErrorFor(x => x.WelfareTypeId);
    }

    [Test]
    public void Validate_ShouldHaveError_WhenWelfareTypeIdIsNegative()
    {
        // Arrange
        var query = new CheckWelfareTypeQuery { LaborId = 1, WelfareTypeId = -1 };

        // Act & Assert
        var result = _validator.TestValidate(query);
        result.ShouldHaveValidationErrorFor(x => x.WelfareTypeId);
    }

    [Test]
    public void Validate_ShouldNotHaveError_WhenAllFieldsAreValid()
    {
        // Arrange
        var query = new CheckWelfareTypeQuery { LaborId = 1, WelfareTypeId = 1 };

        // Act & Assert
        var result = _validator.TestValidate(query);
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Test]
    public void Validate_ShouldHaveMultipleErrors_WhenBothFieldsAreInvalid()
    {
        // Arrange
        var query = new CheckWelfareTypeQuery { LaborId = 0, WelfareTypeId = 0 };

        // Act & Assert
        var result = _validator.TestValidate(query);
        result.ShouldHaveValidationErrorFor(x => x.LaborId);
        result.ShouldHaveValidationErrorFor(x => x.WelfareTypeId);
    }
}
