{"Kestrel": {"Endpoints": {"Http": {"Url": "http://127.0.0.1:5006"}}}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "Enrich": ["FromLogContext"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore": "Warning", "System": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}, "WelfareManagementConfiguration": {"RunAsWindowsService": false, "ConnectionString": "Data Source=:memory:", "CreateCareRequestSheetAtDay": 15, "Messaging": {"Host": {"HostUrl": "rabbitmq://localhost:5672/", "VirtualHost": "/", "Username": "guest", "Password": "guest", "HeartbeatsInSeconds": 5}, "Consumers": []}}, "Origins": "*"}