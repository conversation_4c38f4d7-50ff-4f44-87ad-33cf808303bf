﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
        <RootNamespace>S3.MoL.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Core.Test" Version="0.0.54" />
        <!--<PackageReference Include="coverlet.collector" Version="6.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>-->
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="NUnit" Version="4.3.2" />
        <PackageReference Include="NUnit.Analyzers" Version="4.8.1">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NUnit3TestAdapter" Version="5.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\WelfareManagement.Api\WelfareManagement.Api.csproj" />
    </ItemGroup>
    
    <ItemGroup>
        <Using Include="NUnit.Framework" />
    </ItemGroup>

</Project>
