﻿MERGE INTO [MaritalStatus] WITH (SERIALIZABLE) AS [Target]
USING (VALUES
  (1,N'MaritalStatus001',N'Single',N'أعزب',0,1)
 ,(2,N'MaritalStatus002',N'Married',N'متزوج',0,1)
 ,(3,N'MaritalStatus003',N'Divorced',N'مطلق',0,1)
 ,(4,N'MaritalStatus004',N'Widowed',N'أرمل',0,1)
) AS [Source] ([MaritalStatusID],[Code],[Text],[Text2],[IsDeleted],[IsActive])
ON ([Target].[MaritalStatusID] = [Source].[MaritalStatusID])
WHEN MATCHED THEN
 UPDATE SET
  [Target].[Code] = [Source].[Code], 
  [Target].[Text] = [Source].[Text], 
  [Target].[Text2] = [Source].[Text2], 
  [Target].[IsDeleted] = [Source].[IsDeleted], 
  [Target].[IsActive] = [Source].[IsActive]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([MaritalStatusID],[Code],[Text],[Text2],[IsDeleted],[IsActive])
 VALUES([Source].[MaritalStatusID],[Source].[Code],[Source].[Text],[Source].[Text2],[Source].[IsDeleted],[Source].[IsActive])
WHEN NOT MATCHED BY SOURCE THEN 
 DELETE;