﻿MERGE INTO [RequestStatus] WITH (SERIAL<PERSON>ZABLE) AS [Target]
USING (VALUES
  (1,N'InProgress',N'InProgress',N'جاري التنفيذ')
 ,(2,N'Completed',N'Completed',N'مكتمل')
 ,(3,N'Cancelled',N'Cancelled',N'تم الالغاء')
) AS [Source] ([RequestStatusID],[Code],[Text],[Text2])
ON ([Target].[RequestStatusID] = [Source].[RequestStatusID])
WHEN MATCHED THEN
 UPDATE SET
  [Target].[Code] = [Source].[Code], 
  [Target].[Text] = [Source].[Text], 
  [Target].[Text2] = [Source].[Text2]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([RequestStatusID],[Code],[Text],[Text2])
 VALUES([Source].[RequestStatusID],[Source].[Code],[Source].[Text],[Source].[Text2])
WHEN NOT MATCHED BY SOURCE THEN 
 DELETE;