﻿MERGE INTO [WelfareCategory] WITH (SERIALIZABLE) AS [Target]
USING (VALUES
    (1, N'WC001', N'Monetary',   N'نقدي'),
    (2, N'WC002', N'Perceptible',  N'عيني')
) AS [Source] ([WelfareCategoryId], [Code], [Text], [Text2])
ON [Target].[WelfareCategoryId] = [Source].[WelfareCategoryId]
WHEN MATCHED THEN
    UPDATE SET
        [Code] = [Source].[Code],
        [Text] = [Source].[Text],
        [Text2] = [Source].[Text2]
WHEN NOT MATCHED BY TARGET THEN
    INSERT ([WelfareCategoryId], [Code], [Text], [Text2])
    VALUES ([Source].[WelfareCategoryId], [Source].[Code], [Source].[Text], [Source].[Text2])
WHEN NOT MATCHED BY SOURCE THEN
    DELETE;
