﻿MERGE INTO [BusinessNature] AS [Target]
USING (VALUES
  (1,N'WI-001',N'المقاولات',N'المقاولات',0,1)
 ,(2,N'WI-002',N'الزراعة',N'الزراعة',0,1)
 ,(3,N'WI-003',N'البترول',N'البترول',0,1)
 ,(4,N'WI-004',N'المحاجر',N'المحاجر',0,1)
) AS [Source] ([BusinessNatureID],[Code],[Text],[Text2],[IsDeleted],[IsActive])
ON (([Target].[BusinessNatureID] = [Source].[BusinessNatureID] OR ([Target].[BusinessNatureID] IS NULL AND [Source].[BusinessNatureID] IS NULL)))
WHEN MATCHED THEN
 UPDATE SET
  [Target].[BusinessNatureID] = [Source].[BusinessNatureID], 
  [Target].[Code] = [Source].[Code], 
  [Target].[Text] = [Source].[Text], 
  [Target].[Text2] = [Source].[Text2], 
  [Target].[IsDeleted] = [Source].[IsDeleted], 
  [Target].[IsActive] = [Source].[IsActive]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([BusinessNatureID],[Code],[Text],[Text2],[IsDeleted],[IsActive])
 VALUES([BusinessNatureID],[Code],[Text],[Text2],[IsDeleted],[IsActive])
WHEN NOT MATCHED BY SOURCE THEN 
 DELETE;