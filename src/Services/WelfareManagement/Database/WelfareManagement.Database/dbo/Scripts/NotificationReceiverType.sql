﻿MERGE INTO [NotificationReceiverType] WITH (SERIAL<PERSON>ZABLE) AS [Target]
USING (VALUES
  (1,N'All',N'All',N'الكل')
 ,(2,N'Directorate',N'Directorate',N'المديرية')
 ,(3,N'Labor',N'Labor',N'العامل')
) AS [Source] ([NotificationReceiverTypeID],[Code],[Text],[Text2])
ON ([Target].[NotificationReceiverTypeID] = [Source].[NotificationReceiverTypeID])
WHEN MATCHED THEN
 UPDATE SET
  [Target].[Code] = [Source].[Code], 
  [Target].[Text] = [Source].[Text], 
  [Target].[Text2] = [Source].[Text2]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([NotificationReceiverTypeID],[Code],[Text],[Text2])
 VALUES([Source].[NotificationReceiverTypeID],[Source].[Code],[Source].[Text],[Source].[Text2])
WHEN NOT MATCHED BY SOURCE THEN 
 DELETE;