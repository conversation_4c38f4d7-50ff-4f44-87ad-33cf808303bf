﻿MERGE INTO [PartyType] WITH (SERI<PERSON><PERSON><PERSON>ABLE) AS [Target]
USING (VALUES
  (1, N'C0010', N'Individual', N'افراد', 0),
  (2, N'C0011', N'Company', N'شركة', 0),
  (3, N'C0012', N'Government', N'حكومة', 0)
) AS [Source] ([PartyTypeID], [Code], [Text], [Text2], [IsDeleted])
ON ([Target].[PartyTypeID] = [Source].[PartyTypeID])
WHEN MATCHED THEN
  UPDATE SET
    [Target].[Code] = [Source].[Code], 
    [Target].[Text] = [Source].[Text], 
    [Target].[Text2] = [Source].[Text2],
    [Target].[IsDeleted] = [Source].[IsDeleted]
WHEN NOT MATCHED BY TARGET THEN
  INSERT ([PartyTypeID], [Code], [Text], [Text2], [IsDeleted])
  VALUES ([Source].[PartyTypeID], [Source].[Code], [Source].[Text], [Source].[Text2], [Source].[IsDeleted])
WHEN NOT MATCHED BY SOURCE THEN 
  DELETE;
