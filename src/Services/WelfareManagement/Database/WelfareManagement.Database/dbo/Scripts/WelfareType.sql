﻿MERGE INTO [WelfareType] WITH (SERIALIZABLE) AS [Target]
USING (VALUES
    (1, N'WT-M_01', N'Marriage', N'زواج',           1, NULL, NULL),
    (2, N'WT-M_02', N'Newborn', N'مولود',           1, NULL, NULL),
    (3, N'WT-M_03', N'Death of the worker', N'وفاة العامل',       1, NULL, NULL),
    (4, N'WT-M_04', N'Relative Family Person Death', N'وفاة أحد الأقارب', 1, NULL, NULL),
    (5, N'WT-M_05', N'Partial Disability', N'عجز جزئي',       1, NULL, NULL),
    (6, N'WT-M_06', N'Full Disability', N'عجز كلى',         1, NULL, NULL)
    --,
    --(7, N'WT-M_07', N'Prosthetic Device', N'جهاز تعويضي',     1, NULL, NULL),
    --(8, N'WT-M_08', N'Small Surgery', N'عملية جراحية صغرى',     1, NULL, NULL),
    --(9, N'WT-M_09', N'Large Surgery', N'عملية جراحية كبرى',     1, NULL, NULL),
    --(10, N'WT-M_10', N'Exceptional', N'استثنائي',        1, NULL, NULL),
    --(11, N'WT-P_01', N'Examination', N'كشف طبي',         2, NULL, NULL),
    --(12, N'WT-P_02', N'Medics', N'صرف علاج',           2, NULL, NULL),
    --(13, N'WT-P_03', N'Laboratory', N'تحاليل',           2, NULL, NULL),
    --(14, N'WT-P_04', N'Radiology', N'أشعة',            2, NULL, NULL)
) AS [Source] ([WelfareTypeID], [Code], [Text], [Text2], [FK_WelfareCategoryId], [WelfareAmount], [MaximumLimit])
ON [Target].[WelfareTypeID] = [Source].[WelfareTypeID]
WHEN MATCHED THEN
    UPDATE SET
        [Code] = [Source].[Code],
        [Text] = [Source].[Text],
        [Text2] = [Source].[Text2],
        [FK_WelfareCategoryId] = [Source].[FK_WelfareCategoryId],
        [WelfareAmount] = [Source].[WelfareAmount],
        [MaximumLimit] = [Source].[MaximumLimit]
WHEN NOT MATCHED BY TARGET THEN
    INSERT ([WelfareTypeID], [Code], [Text], [Text2], [FK_WelfareCategoryId], [WelfareAmount], [MaximumLimit])
    VALUES ([Source].[WelfareTypeID], [Source].[Code], [Source].[Text], [Source].[Text2], [Source].[FK_WelfareCategoryId], [Source].[WelfareAmount], [Source].[MaximumLimit])
WHEN NOT MATCHED BY SOURCE THEN
    DELETE;
