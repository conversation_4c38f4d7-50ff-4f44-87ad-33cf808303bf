﻿
MERGE INTO [AttachmentType] AS [Target]
USING (VALUES
  (1,N'MARR_CERT',N'Marriage Certificate',N'قسيمة الزواج',2,'.JPG,.JPEG,.PNG,.GIF,.BMP,.SVG,.TIFF,.PDF',1,1),
  (2,N'BIRTH_CERT',N'Birth Certificate',N'شهادة الميلاد',2,'.JPG,.JPEG,.PNG,.GIF,.BMP,.SVG,.TIFF,.PDF',1,1),
  (3,N'DEATH_CERT',N'Death Certificate',N'شهادة الوفاة',2,'.JPG,.JPEG,.PNG,.GIF,.BMP,.SVG,.TIFF,.PDF',1,1),
  (4,N'RELATIVE_CERT',N'Proof of kinship',N'اثبات صلة القرابة',2,'.JPG,.JPEG,.PNG,.GIF,.BMP,.SVG,.TIFF,.PDF',1,1),
  (5,N'NID_CERT',N'National ID card',N'بطاقة الرقم القومي',2,'.JPG,.JPEG,.PNG,.GIF,.BMP,.SVG,.TIFF,.PDF',1,1),
  (6,N'INHER_CERT',N'Inheritance Certificate',N'إعلام الوراثة',2,'.JPG,.JPEG,.PNG,.GIF,.BMP,.SVG,.TIFF,.PDF',1,1),
  (7,N'NID_CERT',N'National ID card',N'بطاقة الرقم القومي',2,'.JPG,.JPEG,.PNG,.GIF,.BMP,.SVG,.TIFF,.PDF',1,10),
  (8,N'MED_CERT',N'Medical report',N'التقرير الطبي',2,'.JPG,.JPEG,.PNG,.GIF,.BMP,.SVG,.TIFF,.PDF',1,1)
) AS [Source] ([AttachmentTypeID],[Code],[Text],[Text2],[SizeLimit],[MimeTypes],[Mandatory],[MaxFileCount])
ON [Target].[AttachmentTypeID] = [Source].[AttachmentTypeID]
WHEN MATCHED THEN
 UPDATE SET
  [Target].[Code] = [Source].[Code], 
  [Target].[Text] = [Source].[Text], 
  [Target].[Text2] = [Source].[Text2], 
  [Target].[SizeLimit] = [Source].[SizeLimit], 
  [Target].[MimeTypes] = [Source].[MimeTypes], 
  [Target].[Mandatory] = [Source].[Mandatory], 
  [Target].[MaxFileCount] = [Source].[MaxFileCount]
WHEN NOT MATCHED THEN
 INSERT([AttachmentTypeID],[Code],[Text],[Text2],[SizeLimit],[MimeTypes],[Mandatory],[MaxFileCount])
 VALUES([Source].[AttachmentTypeID],[Source].[Code],[Source].[Text],[Source].[Text2],[Source].[SizeLimit],[Source].[MimeTypes],[Source].[Mandatory],[Source].[MaxFileCount])
WHEN NOT MATCHED BY SOURCE THEN 
 DELETE;