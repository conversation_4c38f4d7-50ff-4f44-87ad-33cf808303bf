﻿MERGE INTO [Gender] WITH (SERIALIZABLE) AS [Target]
USING (VALUES
   (1, N'Gender001', N'Male',   N'ذكر',  0, 1),
   (2, N'Gender002', N'Female', N'انثى', 0, 1)
) AS [Source] ([GenderID], [Code], [Text], [Text2], [IsDeleted], [IsActive])
ON [Target].[GenderID] = [Source].[GenderID]
WHEN MATCHED THEN
    UPDATE SET
        [Code] = [Source].[Code],
        [Text] = [Source].[Text],
        [Text2] = [Source].[Text2],
        [IsDeleted] = [Source].[IsDeleted],
        [IsActive] = [Source].[IsActive]
WHEN NOT MATCHED BY TARGET THEN
    INSERT ([GenderID], [Code], [Text], [Text2], [IsDeleted], [IsActive])
    VALUES ([Source].[GenderID], [Source].[Code], [Source].[Text], [Source].[Text2], [Source].[IsDeleted], [Source].[IsActive])
WHEN NOT MATCHED BY SOURCE THEN
    DELETE;
