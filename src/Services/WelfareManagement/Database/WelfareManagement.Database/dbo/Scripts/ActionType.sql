﻿MERGE INTO [ActionType] WITH (SE<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) AS [Target]
USING (VALUES
  (1, N'ACT-SUBMIT',  N'Submit',  N'إرسال'),
  (2, N'ACT-APPROVE', N'Approve', N'اعتماد'),
  (3, N'ACT-RETURN',  N'Return',  N'إرجاع'),
  (4, N'ACT-REJECT',  N'Reject',  N'رفض'),
  (5, N'ACT-CANCEL',  N'Cancel',  N'إلغاء')
) AS [Source] ([ActionTypeId], [Code], [Text], [Text2])
ON [Target].[ActionTypeId] = [Source].[ActionTypeId]
WHEN MATCHED THEN
  UPDATE SET 
    [Code]  = [Source].[Code],
    [Text]  = [Source].[Text],
    [Text2] = [Source].[Text2]
WHEN NOT MATCHED BY TARGET THEN
  INSERT ([ActionTypeId], [Code], [Text], [Text2])
  VALUES ([Source].[ActionTypeId], [Source].[Code], [Source].[Text], [Source].[Text2])
WHEN NOT MATCHED BY SOURCE THEN
  DELETE;

