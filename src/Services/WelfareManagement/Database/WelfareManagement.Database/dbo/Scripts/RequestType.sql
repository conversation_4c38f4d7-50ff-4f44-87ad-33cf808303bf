﻿MERGE INTO [RequestType] WITH (SERIALIZABLE) AS [Target]
USING (VALUES
  (1, N'Welfare',    N'Welfare Request',       N'طلب رعاية', 1),
  (2, N'AnnualReward',  N'Annual Reward',                N'طلب منح دورية',       0)
) AS [Source] ([RequestTypeID], [Code], [Text], [Text2], [IsMemorandum])
ON ([Target].[RequestTypeID] = [Source].[RequestTypeID])
WHEN MATCHED THEN
  UPDATE SET
    [Target].[Code] = [Source].[Code], 
    [Target].[Text] = [Source].[Text], 
    [Target].[Text2] = [Source].[Text2],
    [Target].[IsMemorandum] = [Source].[IsMemorandum]
WHEN NOT MATCHED BY TARGET THEN
  INSERT ([RequestTypeID], [Code], [Text], [Text2], [IsMemorandum])
  VALUES ([Source].[RequestTypeID], [Source].[Code], [Source].[Text], [Source].[Text2], [Source].[IsMemorandum])
WHEN NOT MATCHED BY SOURCE THEN 
  DELETE;
