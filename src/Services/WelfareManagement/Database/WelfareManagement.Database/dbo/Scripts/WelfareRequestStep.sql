﻿MERGE INTO [WelfareRequestStep] WITH (SERIAL<PERSON>ZABLE) AS [Target]
USING (VALUES
    (1, N'Initial Data Entry', N'STEP_INITIAL_ENTRY', N'إدخال البيانات الأولية'),
    (2, N'Review the Request', N'STEP_REVIEW_REQUEST', N'مراجعة الطلب')
) AS [Source] ([WelfareRequestStepId], [Text], [Code], [Text2])
ON [Target].[WelfareRequestStepId] = [Source].[WelfareRequestStepId]
WHEN MATCHED THEN
    UPDATE SET 
        [Text] = [Source].[Text],
        [Code] = [Source].[Code],
        [Text2] = [Source].[Text2]
WHEN NOT MATCHED BY TARGET THEN
    INSERT ([WelfareRequestStepId], [Text], [Code], [Text2])
    VALUES ([Source].[WelfareRequestStepId], [Source].[Text], [Source].[Code], [Source].[Text2])
WHEN NOT MATCHED BY SOURCE THEN
    DELETE;