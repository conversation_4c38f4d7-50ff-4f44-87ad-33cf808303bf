﻿MERGE INTO [WorkflowReason] WITH (SERIAL<PERSON><PERSON><PERSON><PERSON>) AS [Target]
USING (VALUES
  (1, N'CWorkFlow001', N'خطأ فى ادخال البيانات', N'خطأ فى ادخال البيانات', N'WelfareRequest') 
) AS [Source] ([WorkflowReasonID], [Code], [Text], [Text2], [RequestType])
ON ([Target].[WorkflowReasonID] = [Source].[WorkflowReasonID])
WHEN NOT MATCHED BY TARGET THEN
 INSERT ([WorkflowReasonID], [Code], [Text], [Text2], [RequestType])
 VALUES ([Source].[WorkflowReasonID], [Source].[Code], [Source].[Text], [Source].[Text2], [Source].[RequestType]);
