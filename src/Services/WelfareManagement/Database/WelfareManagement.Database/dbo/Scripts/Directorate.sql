﻿MERGE INTO [Directorate] WITH (SERIALIZABLE) AS [Target]
USING (VALUES
  (1,N'1',N'القاهرة',N'القاهرة',0,1,398)
 ,(2,N'2',N'الجيزة',N'الجيزة',0,1,399)
 ,(3,N'3',N'القليوبية',N'القليوبية',0,1,400)
 ,(4,N'4',N'الإسكندرية',N'الإسكندرية',0,1,401)
 ,(5,N'5',N'الإسماعيلية',N'الإسماعيلية',0,1,402)
 ,(6,N'6',N'السويس',N'السويس',0,1,403)
 ,(7,N'7',N'شمال سيناء',N'شمال سيناء',0,1,404)
 ,(8,N'8',N'أسوان',N'أسوان',0,1,405)
 ,(9,N'9',N'البحر الأحمر',N'البحر الأحمر',0,1,406)
 ,(10,N'10',N'الشرقية',N'الشرقية',0,1,407)
 ,(11,N'11',N'الدقهلية',N'الدقهلية',0,1,408)
 ,(12,N'12',N'دمياط',N'دمياط',0,1,409)
 ,(13,N'13',N'الفيوم',N'الفيوم',0,1,410)
 ,(14,N'14',N'المنيا',N'المنيا',0,1,411)
 ,(15,N'15',N'أسيوط',N'أسيوط',0,1,412)
 ,(16,N'16',N'سوهاج',N'سوهاج',0,1,413)
 ,(17,N'17',N'قنا',N'قنا',0,1,414)
 ,(18,N'18',N'جنوب سيناء',N'جنوب سيناء',0,1,415)
 ,(19,N'19',N'الوادى الجديد',N'الوادى الجديد',0,1,416)
 ,(20,N'20',N'الأقصر',N'الأقصر',0,1,417)
 ,(21,N'21',N'كفر الشيخ',N'كفر الشيخ',0,1,418)
 ,(22,N'22',N'مطروح',N'مطروح',0,1,419)
 ,(23,N'23',N'البحيرة',N'البحيرة',0,1,420)
 ,(24,N'24',N'الغربية',N'الغربية',0,1,421)
 ,(25,N'25',N'بنى سويف',N'بنى سويف',0,1,422)
 ,(26,N'26',N'بورسعيد',N'بورسعيد',0,1,423)
 ,(27,N'27',N'المنوفيه',N'المنوفيه',0,1,424)
) AS [Source] ([DirectorateID],[Code],[Text],[Text2],[IsDeleted],[IsActive],[FK_GovernorateID])
ON ([Target].[DirectorateID] = [Source].[DirectorateID])
WHEN MATCHED THEN
 UPDATE SET
  [Target].[Code] = [Source].[Code], 
  [Target].[Text] = [Source].[Text], 
  [Target].[Text2] = [Source].[Text2], 
  [Target].[IsDeleted] = [Source].[IsDeleted], 
  [Target].[IsActive] = [Source].[IsActive], 
  [Target].[FK_GovernorateID] = [Source].[FK_GovernorateID]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([DirectorateID],[Code],[Text],[Text2],[IsDeleted],[IsActive],[FK_GovernorateID])
 VALUES([Source].[DirectorateID],[Source].[Code],[Source].[Text],[Source].[Text2],[Source].[IsDeleted],[Source].[IsActive],[Source].[FK_GovernorateID])
WHEN NOT MATCHED BY SOURCE THEN 
 DELETE;
