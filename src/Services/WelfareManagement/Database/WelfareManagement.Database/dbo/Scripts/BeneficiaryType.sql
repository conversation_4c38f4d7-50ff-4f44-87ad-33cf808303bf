﻿MERGE INTO [BeneficiaryType] WITH (SERI<PERSON><PERSON><PERSON>ABLE) AS [Target]
USING (VALUES
    (1, N'BT001', N'Worker',           N'عامل'),
    (2, N'BT002', N'Service Provider', N'مقدم خدمة'),
    (3, N'BT003', N'other', N'اخري')
) AS [Source] ([BeneficiaryTypeId], [Code], [Text], [Text2])
ON [Target].[BeneficiaryTypeId] = [Source].[BeneficiaryTypeId]
WHEN MATCHED THEN
    UPDATE SET
        [Code] = [Source].[Code],
        [Text] = [Source].[Text],
        [Text2] = [Source].[Text2]
WHEN NOT MATCHED BY TARGET THEN
    INSERT ([BeneficiaryTypeId], [Code], [Text], [Text2])
    VALUES ([Source].[BeneficiaryTypeId], [Source].[Code], [Source].[Text], [Source].[Text2])
WHEN NOT MATCHED BY SOURCE THEN
    DELETE;
