﻿MERGE INTO [RelativeRelationType] WITH (SERIAL<PERSON>ZABLE) AS [Target]
USING (VALUES
  (1,N'RRT001',N'Father',N'أب')
 ,(2,N'RRT002',N'Mother',N'أم')
 ,(3,N'RRT003',N'Grandfather',N'جد')
 ,(4,N'RRT004',N'Grandmother',N'جدة')
 ,(5,N'RRT005',N'Brother',N'أخ')
 ,(6,N'RRT006',N'Sister',N'أخت')
 ,(7,N'RRT007',N'Son',N'ابن')
 ,(8,N'RRT008',N'Daughter',N'ابنة')
 ,(9,N'RRT009',N'Uncle',N'عم')
 ,(10,N'RRT010',N'Aunt',N'عمة')
 ,(11,N'RRT011',N'Husband',N'زوج')
 ,(12,N'RRT012',N'Wife',N'زوجة')
 ,(13,N'RRT013',N'Son of Brother',N'ابن الأخ')
 ,(14,N'RRT014',N'Daughter of Brother',N'ابنة الأخ')
 ,(15,N'RRT015',N'Son of Sister',N'ابن الأخت')
 ,(16,N'RRT016',N'Daughter of Sister',N'ابنة الأخت')
 ,(17,N'RRT017',N'Grandson',N'حفيد')
 ,(18,N'RRT018',N'Granddaughter',N'حفيدة')
 ,(19,N'RRT019',N'Cousin (male)',N'ابن العم')
 ,(20,N'RRT020',N'Cousin (female)',N'ابنة العم')
 ,(21,N'RRT021',N'Stepfather',N'زوج الأم')
 ,(22,N'RRT022',N'Stepmother',N'زوجة الأب')
 ,(23,N'RRT023',N'Son of Husband',N'ابن الزوج')
 ,(24,N'RRT024',N'Daughter of Husband',N'ابنة الزوج')
 ,(25,N'RRT025',N'Son of Wife',N'ابن الزوجة')
 ,(26,N'RRT026',N'Daughter of Wife',N'ابنة الزوجة')
 ,(27,N'RRT027',N'Brother-in-law',N'شقيق الزوج')
 ,(28,N'RRT028',N'Sister-in-law',N'شقيقة الزوجة')
) AS [Source] ([RelativeRelationTypeID],[Code],[Text],[Text2])
ON ([Target].[RelativeRelationTypeID] = [Source].[RelativeRelationTypeID])
WHEN MATCHED THEN
 UPDATE SET
  [Target].[Code] = [Source].[Code], 
  [Target].[Text] = [Source].[Text], 
  [Target].[Text2] = [Source].[Text2]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([RelativeRelationTypeID],[Code],[Text],[Text2])
 VALUES([Source].[RelativeRelationTypeID],[Source].[Code],[Source].[Text],[Source].[Text2])
WHEN NOT MATCHED BY SOURCE THEN 
 DELETE;