﻿MERGE INTO [WelfareTypesAttachmentTypes] AS Target
USING (VALUES
    (1,1),
    (2,2),
    (3,3),
    (3,6),
    (3,7),
    (4,3),
    (4,4),
    (5,8),
    (6,8)
) AS Source ([WelfareTypeID], [AttachmentTypeID])
ON Target.WelfareTypeID = Source.WelfareTypeID AND Target.AttachmentTypeID = Source.AttachmentTypeID
WHEN NOT MATCHED BY TARGET THEN
    INSERT ([WelfareTypeID], [AttachmentTypeID])
    VALUES (Source.WelfareTypeID, Source.AttachmentTypeID)
WHEN NOT MATCHED BY SOURCE THEN
    DELETE;