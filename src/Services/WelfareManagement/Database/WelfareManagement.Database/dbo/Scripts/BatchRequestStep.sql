﻿MERGE INTO [BatchRequestStep] WITH (SERIALIZABLE) AS [Target]
USING (VALUES
    (1, N'إنشاء الدفعة وتقديمها', N'STEP_CREATE_SUBMIT', N'Batch Creation and submission'),
    (2, N'مراجعة فنية لطلبات الدعم', N'STEP_TECH_REVIEW', N'Technical revision on welfare requests'),
    (3, N'القرار النهائي للإدارة المركزية', N'STEP_FINAL_DECISION', N'Central admin final decision')
) AS [Source] ([BatchRequestStepId], [Text2], [Code], [Text])
ON [Target].[BatchRequestStepId] = [Source].[BatchRequestStepId]
WHEN MATCHED THEN
    UPDATE SET 
        [Text2] = [Source].[Text2],
        [Code] = [Source].[Code],
        [Text] = [Source].[Text]
WHEN NOT MATCHED BY TARGET THEN
    INSERT ([BatchRequestStepId], [Text2], [Code], [Text])
    VALUES ([Source].[BatchRequestStepId], [Source].[Text2], [Source].[Code], [Source].[Text])
WHEN NOT MATCHED BY SOURCE THEN
    DELETE;