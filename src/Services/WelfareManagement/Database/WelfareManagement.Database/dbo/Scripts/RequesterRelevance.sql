﻿MERGE INTO [RequesterRelevance] WITH (SERIAL<PERSON><PERSON><PERSON><PERSON>) AS [Target]
USING (VALUES
  (1, N'RR001', N'Delegator', N'المفوِّض'),
  (2, N'RR002', N'Agent',  N'موكل'),
  (3, N'RR003', N'Lawyer',    N'محامٍ')
) AS [Source] ([RequesterRelevantID], [Code], [Text], [Text2])
ON [Target].[RequesterRelevantID] = [Source].[RequesterRelevantID]
WHEN MATCHED THEN
  UPDATE SET 
    [Code] = [Source].[Code],
    [Text] = [Source].[Text],
    [Text2] = [Source].[Text2]
WHEN NOT MATCHED BY TARGET THEN
  INSERT ([RequesterRelevantID], [Code], [Text], [Text2])
  VALUES ([Source].[RequesterRelevantID], [Source].[Code], [Source].[Text], [Source].[Text2])
WHEN NOT MATCHED BY SOURCE THEN 
  DELETE;
