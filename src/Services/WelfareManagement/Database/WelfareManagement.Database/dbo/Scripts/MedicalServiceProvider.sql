﻿MERGE INTO [MedicalServiceProvider] WITH (SERIALIZABLE) AS [Target]
USING (VALUES
    (1, N'MSP001', N'Dar Elfouad Hospital',        N' مستشفى دار الفؤاد',       N'EG38 0120 0000 0000 0020 0000 0020'),
    (2, N'MSP002', N'Almokhtabar Laboratory',      N'معمل المختبر',   N'EG38 0120 0000 0000 0020 0000 0021')
) AS [Source] ([MedicalServiceProviderId], [Code], [Text], [Text2], [IBAN])
ON [Target].[MedicalServiceProviderId] = [Source].[MedicalServiceProviderId]
WHEN MATCHED THEN
    UPDATE SET
        [Code] = [Source].[Code],
        [Text] = [Source].[Text],
        [Text2] = [Source].[Text2],
        [IBAN] = [Source].[IBAN]
WHEN NOT MATCHED BY TARGET THEN
    INSERT ([MedicalServiceProviderId], [Code], [Text], [Text2], [IBAN])
    VALUES ([Source].[MedicalServiceProviderId], [Source].[Code], [Source].[Text], [Source].[Text2], [Source].[IBAN])
WHEN NOT MATCHED BY SOURCE THEN
    DELETE;
