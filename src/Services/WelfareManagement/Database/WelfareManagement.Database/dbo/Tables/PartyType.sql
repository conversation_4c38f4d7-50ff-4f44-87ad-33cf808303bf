﻿CREATE TABLE [dbo].[PartyType](
	[PartyTypeID] [int] NOT NULL,
	[Code] [nvarchar](30) NOT NULL,
	[Text] [nvarchar](50) NOT NULL,
	[Text2] [nvarchar](50) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsActive] [bit] NOT NULL,
 CONSTRAINT [PK_PartyType] PRIMARY KEY CLUSTERED 
(
	[PartyTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Indicates if the business nature is deleted' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'PartyType', @level2type=N'COLUMN',@level2name=N'IsDeleted'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Indicates if the business nature is active' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'PartyType', @level2type=N'COLUMN',@level2name=N'IsActive'
GO
ALTER TABLE [dbo].[PartyType] ADD  DEFAULT (CONVERT([bit],(1))) FOR [IsActive]