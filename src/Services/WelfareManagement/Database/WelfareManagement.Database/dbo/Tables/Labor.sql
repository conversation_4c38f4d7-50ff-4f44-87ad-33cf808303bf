CREATE TABLE [dbo].[Labor] (
    [LaborID]                 BIGINT         NOT NULL,
    [NationalID]              CHAR (14)      NOT NULL,
    [FullName]                NVARCHAR (200) NOT NULL,
    [BirthDate]               DATE           NULL,
    [FK_GenderID]             INT            NULL,
    [FK_MaritalStatusID]      INT            NULL,
    [FK_OccupationID]         INT            NOT NULL,
    [MobileNo]                CHAR (13)      NOT NULL,
    [FK_LastDirectorateID]    INT            NULL,
    [FK_LastBusinessNatureID] INT            NULL,
    [FK_LastExecutionPartyID] BIGINT         NULL,
    [RegistrationNo]          VARCHAR (20)   NULL,
    [RegistrationDate]        DATE           NULL,
    [InsuranceNo]             CHAR (9)       NULL,
    [DeathDate]               DATE           NULL,
    [HasFullDisability]       BIT            DEFAULT (CONVERT([bit],(0))) NOT NULL,
    [IsBeneficiary]           AS             (case when [DeathDate] IS NOT NULL OR [HasFullDisability]=(1) then CONVERT([bit],(0)) else CONVERT([bit],(1)) end) PERSISTED,
    [UpdatedDate]             DATETIME2 (7)  NULL,
    [UpdatedUserId]           VARCHAR (100)  NULL,
    [UpdatedUserName]         NVARCHAR (200) NULL,
    CONSTRAINT [PK_Labor] PRIMARY KEY CLUSTERED ([LaborID] ASC),
    CONSTRAINT [FK_Labor_BusinessNature_FK_LastBusinessNatureID] FOREIGN KEY ([FK_LastBusinessNatureID]) REFERENCES [dbo].[BusinessNature] ([BusinessNatureID]),
    CONSTRAINT [FK_Labor_Directorate_FK_LastDirectorateID] FOREIGN KEY ([FK_LastDirectorateID]) REFERENCES [dbo].[Directorate] ([DirectorateID]),
    CONSTRAINT [FK_Labor_Gender_FK_GenderID] FOREIGN KEY ([FK_GenderID]) REFERENCES [dbo].[Gender] ([GenderID]),
    CONSTRAINT [FK_Labor_MaritalStatus_FK_MaritalStatusID] FOREIGN KEY ([FK_MaritalStatusID]) REFERENCES [dbo].[MaritalStatus] ([MaritalStatusID]),
    CONSTRAINT [FK_Labor_Occupation_FK_OccupationID] FOREIGN KEY ([FK_OccupationID]) REFERENCES [dbo].[Occupation] ([OccupationID]) ON DELETE CASCADE,
    CONSTRAINT [FK_Labor_Party_FK_LastExecutionPartyID] FOREIGN KEY ([FK_LastExecutionPartyID]) REFERENCES [dbo].[Party] ([PartyID])
);






GO
/****** Object:  Index [IX_Labor_FK_GenderID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Labor_FK_GenderID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Labor_FK_GenderID] ON [dbo].[Labor]
(
	[FK_GenderID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_Labor_FK_LastBusinessNatureID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Labor_FK_LastBusinessNatureID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Labor_FK_LastBusinessNatureID] ON [dbo].[Labor]
(
	[FK_LastBusinessNatureID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_Labor_FK_LastDirectorateID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Labor_FK_LastDirectorateID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Labor_FK_LastDirectorateID] ON [dbo].[Labor]
(
	[FK_LastDirectorateID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_Labor_FK_LastExecutionPartyID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Labor_FK_LastExecutionPartyID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Labor_FK_LastExecutionPartyID] ON [dbo].[Labor]
(
	[FK_LastExecutionPartyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_Labor_FK_MaritalStatusID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Labor_FK_MaritalStatusID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Labor_FK_MaritalStatusID] ON [dbo].[Labor]
(
	[FK_MaritalStatusID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_Labor_FK_OccupationID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Labor_FK_OccupationID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Labor_FK_OccupationID] ON [dbo].[Labor]
(
	[FK_OccupationID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'the worker''s status must be Beneficiary or  Not Beneficiary, default is Beneficiary(1)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Labor', @level2type=N'COLUMN',@level2name=N'IsBeneficiary'
GO

GO


GO


GO

GO


GO


GO

GO


GO


GO

GO


GO


GO

GO


GO


GO

GO


GO


GO

GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'User name of the user who last updated the request record', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Labor', @level2type = N'COLUMN', @level2name = N'UpdatedUserName';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'User ID of the user who last updated the request record', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Labor', @level2type = N'COLUMN', @level2name = N'UpdatedUserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Date and time when the request record was last updated', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Labor', @level2type = N'COLUMN', @level2name = N'UpdatedDate';

