﻿CREATE TABLE [dbo].[MedicalWelfareRequest](
	[RequestId] [bigint] NOT NULL,
	[FK_MedicalServiceProviderId] [int] NULL,
	[Description] [nvarchar](250) NOT NULL,
	[FK_BeneficiaryTypeId] [int] NULL,
	[BeneficiaryName] [nvarchar](120) NULL,
	[BeneficiaryNId] [int] NULL,
	[BeneficiaryIBAN] [nvarchar](120) NULL,
	[NoOfPrescriptions] [tinyint] NULL,
 CONSTRAINT [PK_MedicalWelfareRequest] PRIMARY KEY CLUSTERED 
(
	[RequestId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
/****** Object:  Index [IX_MedicalWelfareRequest_FK_BeneficiaryTypeId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_MedicalWelfareRequest_FK_BeneficiaryTypeId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_MedicalWelfareRequest_FK_BeneficiaryTypeId] ON [dbo].[MedicalWelfareRequest]
(
	[FK_BeneficiaryTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_MedicalWelfareRequest_FK_MedicalServiceProviderId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_MedicalWelfareRequest_FK_MedicalServiceProviderId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_MedicalWelfareRequest_FK_MedicalServiceProviderId] ON [dbo].[MedicalWelfareRequest]
(
	[FK_MedicalServiceProviderId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[MedicalWelfareRequest]  ADD  CONSTRAINT [FK_MedicalWelfareRequest_MedicalServiceProvider_FK_MedicalServiceProviderId] FOREIGN KEY([FK_MedicalServiceProviderId])
REFERENCES [dbo].[MedicalServiceProvider] ([MedicalServiceProviderId])
GO

ALTER TABLE [dbo].[MedicalWelfareRequest] CHECK CONSTRAINT [FK_MedicalWelfareRequest_MedicalServiceProvider_FK_MedicalServiceProviderId]
GO


GO
ALTER TABLE [dbo].[MedicalWelfareRequest]  ADD  CONSTRAINT [FK_MedicalWelfareRequest_BeneficiaryType_FK_BeneficiaryTypeId] FOREIGN KEY([FK_BeneficiaryTypeId])
REFERENCES [dbo].[BeneficiaryType] ([BeneficiaryTypeId])
GO

ALTER TABLE [dbo].[MedicalWelfareRequest] CHECK CONSTRAINT [FK_MedicalWelfareRequest_BeneficiaryType_FK_BeneficiaryTypeId]
GO

