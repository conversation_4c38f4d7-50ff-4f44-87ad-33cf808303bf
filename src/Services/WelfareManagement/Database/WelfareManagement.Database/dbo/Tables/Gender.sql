﻿CREATE TABLE [dbo].[Gender](
	[GenderID] [int] NOT NULL,
	[Code] [nvarchar](30) NOT NULL,
	[Text] [nvarchar](50) NOT NULL,
	[Text2] [nvarchar](50) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsActive] [bit] NOT NULL,
 CONSTRAINT [PK_Gender] PRIMARY KEY CLUSTERED 
(
	[GenderID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Table storing gender information' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Gender'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Unique identifier for the gender' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Gender', @level2type=N'COLUMN',@level2name=N'GenderID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Code representing the gender' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Gender', @level2type=N'COLUMN',@level2name=N'Code'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'English text description of the gender' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Gender', @level2type=N'COLUMN',@level2name=N'Text'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Arabic text description of the gender' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Gender', @level2type=N'COLUMN',@level2name=N'Text2'
GO
ALTER TABLE [dbo].[Gender] ADD  DEFAULT (CONVERT([bit],(1))) FOR [IsActive]