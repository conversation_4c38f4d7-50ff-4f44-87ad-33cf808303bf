﻿CREATE TABLE [dbo].[SocialWelfareRequest] (
    [WelfareRequestId]          BIGINT        NOT NULL,
    [EventDate]                 DATE          NOT NULL,
    [NationalId]                CHAR (14)     NOT NULL,
    [IsTwin]                    BIT           NULL,
    [RequesterName]             NVARCHAR (60) NULL,
    [FK_RelativeRelationshipId] INT           NULL,
    [FK_RequesterRelevanceId]   INT           NULL,
    CONSTRAINT [PK_SocialWelfareRequest] PRIMARY KEY CLUSTERED ([WelfareRequestId] ASC),
    CONSTRAINT [FK_SocialWelfareRequest_RelativeRelationship_FK_RelativeRelationshipId] FOREIGN KEY ([FK_RelativeRelationshipId]) REFERENCES [dbo].[RelativeRelationship] ([RelativeRelationshipID]),
    CONSTRAINT [FK_SocialWelfareRequest_RequesterRelevance_FK_RequesterRelevanceId] FOREIGN KEY ([FK_RequesterRelevanceId]) REFERENCES [dbo].[RequesterRelevance] ([RequesterRelevantID]),
    CONSTRAINT [FK_SocialWelfareRequest_WelfareRequest_WelfareRequestId] FOREIGN KEY ([WelfareRequestId]) REFERENCES [dbo].[WelfareRequest] ([WelfareRequestId]) ON DELETE CASCADE
);




GO
/****** Object:  Index [IX_SocialWelfareRequest_FK_RelativeRelationshipId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_SocialWelfareRequest_FK_RelativeRelationshipId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_SocialWelfareRequest_FK_RelativeRelationshipId] ON [dbo].[SocialWelfareRequest]
(
	[FK_RelativeRelationshipId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_SocialWelfareRequest_FK_RequesterRelevanceId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_SocialWelfareRequest_FK_RequesterRelevanceId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_SocialWelfareRequest_FK_RequesterRelevanceId] ON [dbo].[SocialWelfareRequest]
(
	[FK_RequesterRelevanceId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO

GO


GO


GO

GO


GO


GO

GO


GO

