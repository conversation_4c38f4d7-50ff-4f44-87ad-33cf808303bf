﻿CREATE TABLE [dbo].[ActionTypeWelfareRequestStep](
	[ActionTypesActionTypeId] [int] NOT NULL,
	[WelfareRequestStepsWelfareRequestStepId] [int] NOT NULL,
 CONSTRAINT [PK_ActionTypeWelfareRequestStep] PRIMARY KEY CLUSTERED 
(
	[ActionTypesActionTypeId] ASC,
	[WelfareRequestStepsWelfareRequestStepId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
/****** Object:  Index [IX_ActionTypeWelfareRequestStep_WelfareRequestStepsWelfareRequestStepId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_ActionTypeWelfareRequestStep_WelfareRequestStepsWelfareRequestStepId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_ActionTypeWelfareRequestStep_WelfareRequestStepsWelfareRequestStepId] ON [dbo].[ActionTypeWelfareRequestStep]
(
	[WelfareRequestStepsWelfareRequestStepId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[ActionTypeWelfareRequestStep]  ADD  CONSTRAINT [FK_ActionTypeWelfareRequestStep_WelfareRequestStep_WelfareRequestStepsWelfareRequestStepId] FOREIGN KEY([WelfareRequestStepsWelfareRequestStepId])
REFERENCES [dbo].[WelfareRequestStep] ([WelfareRequestStepId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ActionTypeWelfareRequestStep] CHECK CONSTRAINT [FK_ActionTypeWelfareRequestStep_WelfareRequestStep_WelfareRequestStepsWelfareRequestStepId]
GO


GO
ALTER TABLE [dbo].[ActionTypeWelfareRequestStep]  ADD  CONSTRAINT [FK_ActionTypeWelfareRequestStep_ActionType_ActionTypesActionTypeId] FOREIGN KEY([ActionTypesActionTypeId])
REFERENCES [dbo].[ActionType] ([ActionTypeId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ActionTypeWelfareRequestStep] CHECK CONSTRAINT [FK_ActionTypeWelfareRequestStep_ActionType_ActionTypesActionTypeId]
GO

