﻿CREATE TABLE [dbo].[MaritalStatus](
	[MaritalStatusID] [int] NOT NULL,
	[Code] [nvarchar](30) NOT NULL,
	[Text] [nvarchar](50) NOT NULL,
	[Text2] [nvarchar](50) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsActive] [bit] NOT NULL,
 CONSTRAINT [PK_MaritalStatus] PRIMARY KEY CLUSTERED 
(
	[MaritalStatusID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Unique identifier for each marital status record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'MaritalStatus', @level2type=N'COLUMN',@level2name=N'MaritalStatusID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Code representing the marital status' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'MaritalStatus', @level2type=N'COLUMN',@level2name=N'Code'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'English text description of the marital status' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'MaritalStatus', @level2type=N'COLUMN',@level2name=N'Text'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Arabic text description of the marital status' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'MaritalStatus', @level2type=N'COLUMN',@level2name=N'Text2'
GO
ALTER TABLE [dbo].[MaritalStatus] ADD  DEFAULT (CONVERT([bit],(1))) FOR [IsActive]