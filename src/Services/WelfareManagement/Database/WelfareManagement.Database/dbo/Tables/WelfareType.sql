﻿CREATE TABLE [dbo].[WelfareType](
	[WelfareTypeID] [int] NOT NULL,
	[Code] [nvarchar](30) NOT NULL,
	[Text] [nvarchar](50) NOT NULL,
	[Text2] [nvarchar](50) NULL,
	[FK_WelfareCategoryId] [tinyint] NOT NULL,
	[WelfareAmount] [decimal](8, 2) NULL,
	[MaximumLimit] [tinyint] NULL,
 CONSTRAINT [PK_WelfareType] PRIMARY KEY CLUSTERED 
(
	[WelfareTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
/****** Object:  Index [IX_WelfareType_FK_WelfareCategoryId]    Script Date: 22/05/2025 1:18:15 PM ******/
/****** Object:  Index [IX_WelfareType_FK_WelfareCategoryId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_WelfareType_FK_WelfareCategoryId] ON [dbo].[WelfareType]
(
	[FK_WelfareCategoryId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Unique identifier for each Welfare type' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareType', @level2type=N'COLUMN',@level2name=N'WelfareTypeID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Code representing the Welfare type' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareType', @level2type=N'COLUMN',@level2name=N'Code'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'English text description of the Welfare type' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareType', @level2type=N'COLUMN',@level2name=N'Text'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Arabic text description of the Welfare type' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareType', @level2type=N'COLUMN',@level2name=N'Text2'
GO
ALTER TABLE [dbo].[WelfareType]  ADD  CONSTRAINT [FK_WelfareType_WelfareCategory_FK_WelfareCategoryId] FOREIGN KEY([FK_WelfareCategoryId])
REFERENCES [dbo].[WelfareCategory] ([WelfareCategoryId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[WelfareType] CHECK CONSTRAINT [FK_WelfareType_WelfareCategory_FK_WelfareCategoryId]
GO

