﻿CREATE TABLE [dbo].[RequestStatus](
	[RequestStatusID] [int] NOT NULL,
	[Code] [nvarchar](30) NOT NULL,
	[Text] [nvarchar](50) NOT NULL,
	[Text2] [nvarchar](50) NULL,
 CONSTRAINT [PK_RequestStatus] PRIMARY KEY CLUSTERED 
(
	[RequestStatusID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Unique identifier for each request status record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RequestStatus', @level2type=N'COLUMN',@level2name=N'RequestStatusID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Code representing the request status' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RequestStatus', @level2type=N'COLUMN',@level2name=N'Code'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'English text description of the request status' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RequestStatus', @level2type=N'COLUMN',@level2name=N'Text'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Arabic text description of the request status' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'RequestStatus', @level2type=N'COLUMN',@level2name=N'Text2'