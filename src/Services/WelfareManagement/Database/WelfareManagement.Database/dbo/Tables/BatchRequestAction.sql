﻿CREATE TABLE [dbo].[BatchRequestAction](
	[BatchRequestActionId] [bigint] IDENTITY(1,1) NOT NULL,
	[FK_WorkflowReasonID] [int] NULL,
	[FK_BatchRequestID] [int] NOT NULL,
	[FK_BatchRequestStepId] [int] NOT NULL,
	[FK_ActionTypeID] [int] NOT NULL,
	[CreatedByUserName] [nvarchar](200) NOT NULL,
	[CreatedDate] [datetime2](7) NOT NULL,
	[CreatedByUserId] [varchar](100) NOT NULL,
 CONSTRAINT [PK_BatchRequestAction] PRIMARY KEY CLUSTERED 
(
	[BatchRequestActionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
/****** Object:  Index [IX_BatchRequestAction_FK_ActionTypeID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_BatchRequestAction_FK_ActionTypeID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_BatchRequestAction_FK_ActionTypeID] ON [dbo].[BatchRequestAction]
(
	[FK_ActionTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_BatchRequestAction_FK_BatchRequestID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_BatchRequestAction_FK_BatchRequestID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_BatchRequestAction_FK_BatchRequestID] ON [dbo].[BatchRequestAction]
(
	[FK_BatchRequestID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_BatchRequestAction_FK_BatchRequestStepId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_BatchRequestAction_FK_BatchRequestStepId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_BatchRequestAction_FK_BatchRequestStepId] ON [dbo].[BatchRequestAction]
(
	[FK_BatchRequestStepId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_BatchRequestAction_FK_WorkflowReasonID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_BatchRequestAction_FK_WorkflowReasonID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_BatchRequestAction_FK_WorkflowReasonID] ON [dbo].[BatchRequestAction]
(
	[FK_WorkflowReasonID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Reason for workflow action, if applicable' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequestAction', @level2type=N'COLUMN',@level2name=N'FK_WorkflowReasonID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Parent request identifier' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequestAction', @level2type=N'COLUMN',@level2name=N'FK_BatchRequestID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Current step in workflow for the action' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequestAction', @level2type=N'COLUMN',@level2name=N'FK_BatchRequestStepId'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Type of action performed' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequestAction', @level2type=N'COLUMN',@level2name=N'FK_ActionTypeID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User name of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequestAction', @level2type=N'COLUMN',@level2name=N'CreatedByUserName'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Date and time when the request was created' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequestAction', @level2type=N'COLUMN',@level2name=N'CreatedDate'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User ID of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequestAction', @level2type=N'COLUMN',@level2name=N'CreatedByUserId'
GO
ALTER TABLE [dbo].[BatchRequestAction]  ADD  CONSTRAINT [FK_BatchRequestAction_WorkflowReason_FK_WorkflowReasonID] FOREIGN KEY([FK_WorkflowReasonID])
REFERENCES [dbo].[WorkflowReason] ([WorkflowReasonId])
GO

ALTER TABLE [dbo].[BatchRequestAction] CHECK CONSTRAINT [FK_BatchRequestAction_WorkflowReason_FK_WorkflowReasonID]
GO


GO
ALTER TABLE [dbo].[BatchRequestAction]  ADD  CONSTRAINT [FK_BatchRequestAction_BatchRequestStep_FK_BatchRequestStepId] FOREIGN KEY([FK_BatchRequestStepId])
REFERENCES [dbo].[BatchRequestStep] ([BatchRequestStepId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[BatchRequestAction] CHECK CONSTRAINT [FK_BatchRequestAction_BatchRequestStep_FK_BatchRequestStepId]
GO


GO
ALTER TABLE [dbo].[BatchRequestAction]  ADD  CONSTRAINT [FK_BatchRequestAction_BatchRequest_FK_BatchRequestID] FOREIGN KEY([FK_BatchRequestID])
REFERENCES [dbo].[BatchRequest] ([BatchRequestId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[BatchRequestAction] CHECK CONSTRAINT [FK_BatchRequestAction_BatchRequest_FK_BatchRequestID]
GO


GO
ALTER TABLE [dbo].[BatchRequestAction]  ADD  CONSTRAINT [FK_BatchRequestAction_ActionType_FK_ActionTypeID] FOREIGN KEY([FK_ActionTypeID])
REFERENCES [dbo].[ActionType] ([ActionTypeId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[BatchRequestAction] CHECK CONSTRAINT [FK_BatchRequestAction_ActionType_FK_ActionTypeID]
GO

