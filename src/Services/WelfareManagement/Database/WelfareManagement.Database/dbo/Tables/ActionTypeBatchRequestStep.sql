﻿CREATE TABLE [dbo].[ActionTypeBatchRequestStep](
	[ActionTypesActionTypeId] [int] NOT NULL,
	[BatchRequestStepsBatchRequestStepId] [int] NOT NULL,
 CONSTRAINT [PK_ActionTypeBatchRequestStep] PRIMARY KEY CLUSTERED 
(
	[ActionTypesActionTypeId] ASC,
	[BatchRequestStepsBatchRequestStepId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
/****** Object:  Index [IX_ActionTypeBatchRequestStep_BatchRequestStepsBatchRequestStepId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_ActionTypeBatchRequestStep_BatchRequestStepsBatchRequestStepId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_ActionTypeBatchRequestStep_BatchRequestStepsBatchRequestStepId] ON [dbo].[ActionTypeBatchRequestStep]
(
	[BatchRequestStepsBatchRequestStepId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[ActionTypeBatchRequestStep]  ADD  CONSTRAINT [FK_ActionTypeBatchRequestStep_BatchRequestStep_BatchRequestStepsBatchRequestStepId] FOREIGN KEY([BatchRequestStepsBatchRequestStepId])
REFERENCES [dbo].[BatchRequestStep] ([BatchRequestStepId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ActionTypeBatchRequestStep] CHECK CONSTRAINT [FK_ActionTypeBatchRequestStep_BatchRequestStep_BatchRequestStepsBatchRequestStepId]
GO


GO
ALTER TABLE [dbo].[ActionTypeBatchRequestStep]  ADD  CONSTRAINT [FK_ActionTypeBatchRequestStep_ActionType_ActionTypesActionTypeId] FOREIGN KEY([ActionTypesActionTypeId])
REFERENCES [dbo].[ActionType] ([ActionTypeId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[ActionTypeBatchRequestStep] CHECK CONSTRAINT [FK_ActionTypeBatchRequestStep_ActionType_ActionTypesActionTypeId]
GO

