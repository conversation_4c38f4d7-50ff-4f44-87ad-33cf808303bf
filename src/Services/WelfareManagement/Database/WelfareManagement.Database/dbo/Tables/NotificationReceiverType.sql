﻿CREATE TABLE [dbo].[NotificationReceiverType](
	[NotificationReceiverTypeID] [int] NOT NULL,
	[Code] [nvarchar](30) NOT NULL,
	[Text] [nvarchar](50) NOT NULL,
	[Text2] [nvarchar](50) NULL,
 CONSTRAINT [PK_NotificationReceiverType] PRIMARY KEY CLUSTERED 
(
	[NotificationReceiverTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'A unique identifier for each notification receiver type.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'NotificationReceiverType', @level2type=N'COLUMN',@level2name=N'NotificationReceiverTypeID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'A unique code representing the notification receiver type.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'NotificationReceiverType', @level2type=N'COLUMN',@level2name=N'Code'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'The English description or name of the notification receiver type.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'NotificationReceiverType', @level2type=N'COLUMN',@level2name=N'Text'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'An optional Arabic description or additional information for the notification receiver type.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'NotificationReceiverType', @level2type=N'COLUMN',@level2name=N'Text2'