﻿CREATE TABLE [dbo].[WelfareRequest] (
    [WelfareRequestId]          BIGINT         IDENTITY (1, 1) NOT NULL,
    [RequestNo]                 NVARCHAR (18)  NOT NULL,
    [FK_LaborId]                INT            NOT NULL,
    [FK_DirectorateID]          INT            NOT NULL,
    [FK_WelfareTypeID]          INT            NOT NULL,
    [FK_RequestStatusID]        INT            NOT NULL,
    [CreatedDate]               DATETIME2 (7)  NOT NULL,
    [CreatedByUserId]           VARCHAR (100)  NOT NULL,
    [CreatedByUserName]         NVARCHAR (200) NOT NULL,
    [UpdatedUserId]             VARCHAR (100)  NULL,
    [UpdatedDate]               DATETIME2 (7)  NULL,
    [UpdatedUserName]           NVARCHAR (200) NULL,
    [Version]                   ROWVERSION     NOT NULL,
    [DueAmount]                 DECIMAL (8, 2) NOT NULL,
    [LaborMobileNo]             NVARCHAR (12)  NULL,
    [FK_MemorandumId]           INT            NULL,
    [FK_BatchId]                INT            NULL,
    [Notes]                     NVARCHAR (256) NULL,
    [FK_WelfareRequestStatusID] INT            DEFAULT ((0)) NOT NULL,
    [RequestUUID]               BIGINT         DEFAULT (CONVERT([bigint],(0))) NOT NULL,
    CONSTRAINT [PK_WelfareRequest] PRIMARY KEY CLUSTERED ([WelfareRequestId] ASC),
    CONSTRAINT [FK_WelfareRequest_BatchRequest_FK_BatchId] FOREIGN KEY ([FK_BatchId]) REFERENCES [dbo].[BatchRequest] ([BatchRequestId]),
    CONSTRAINT [FK_WelfareRequest_Directorate_FK_DirectorateID] FOREIGN KEY ([FK_DirectorateID]) REFERENCES [dbo].[Directorate] ([DirectorateID]) ON DELETE CASCADE,
    CONSTRAINT [FK_WelfareRequest_Memorandum_FK_MemorandumId] FOREIGN KEY ([FK_MemorandumId]) REFERENCES [dbo].[Memorandum] ([MemorandumId]),
    CONSTRAINT [FK_WelfareRequest_RequestStatus_FK_RequestStatusID] FOREIGN KEY ([FK_RequestStatusID]) REFERENCES [dbo].[RequestStatus] ([RequestStatusID]) ON DELETE CASCADE,
    CONSTRAINT [FK_WelfareRequest_WelfareRequestStatus_FK_WelfareRequestStatusID] FOREIGN KEY ([FK_WelfareRequestStatusID]) REFERENCES [dbo].[WelfareRequestStatus] ([RequestStatusID]) ON DELETE CASCADE,
    CONSTRAINT [FK_WelfareRequest_WelfareType_FK_WelfareTypeID] FOREIGN KEY ([FK_WelfareTypeID]) REFERENCES [dbo].[WelfareType] ([WelfareTypeID]) ON DELETE CASCADE
);






GO
/****** Object:  Index [IX_Request_FK_DirectorateID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Request_FK_DirectorateID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Request_FK_DirectorateID] ON [dbo].[WelfareRequest]
(
	[FK_DirectorateID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_Request_FK_StatusID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Request_FK_StatusID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Request_FK_StatusID] ON [dbo].[WelfareRequest]
(
	[FK_RequestStatusID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_Request_FK_WelfareTypeID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Request_FK_WelfareTypeID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Request_FK_WelfareTypeID] ON [dbo].[WelfareRequest]
(
	[FK_WelfareTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_WelfareRequest_FK_BatchId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_WelfareRequest_FK_BatchId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_WelfareRequest_FK_BatchId] ON [dbo].[WelfareRequest]
(
	[FK_BatchId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_WelfareRequest_FK_MemorandumId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_WelfareRequest_FK_MemorandumId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_WelfareRequest_FK_MemorandumId] ON [dbo].[WelfareRequest]
(
	[FK_MemorandumId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Identifier for the directorate associated with the request' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'FK_DirectorateID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Identifier for the type of request' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'FK_WelfareTypeID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Identifier for the current status of a current workflow' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'FK_RequestStatusID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Date and time when the request was created' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'CreatedDate'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User ID of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'CreatedByUserId'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User name of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'CreatedByUserName'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User ID of the user who last updated the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'UpdatedUserId'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Date and time when the request record was last updated' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'UpdatedDate'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User name of the user who last updated the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'UpdatedUserName'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Timestamp for version control of the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequest', @level2type=N'COLUMN',@level2name=N'Version'
GO

GO


GO


GO

GO


GO


GO

GO


GO


GO

GO


GO


GO

GO


GO
CREATE NONCLUSTERED INDEX [IX_WelfareRequest_FK_WelfareRequestStatusID]
    ON [dbo].[WelfareRequest]([FK_WelfareRequestStatusID] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Identifier for the current status of a current workflow', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'WelfareRequest', @level2type = N'COLUMN', @level2name = N'FK_WelfareRequestStatusID';

