﻿CREATE TABLE [dbo].[WelfareTypesAttachmentTypes](
	[WelfareTypeID] [int] NOT NULL,
	[AttachmentTypeID] [int] NOT NULL,
 CONSTRAINT [PK_WelfareTypesAttachmentTypes] PRIMARY KEY CLUSTERED 
(
	[WelfareTypeID] ASC,
	[AttachmentTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
/****** Object:  Index [IX_WelfareTypesAttachmentTypes_AttachmentTypeID]    Script Date: 22/05/2025 1:18:15 PM ******/
/****** Object:  Index [IX_WelfareTypesAttachmentTypes_AttachmentTypeID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_WelfareTypesAttachmentTypes_AttachmentTypeID] ON [dbo].[WelfareTypesAttachmentTypes]
(
	[AttachmentTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[WelfareTypesAttachmentTypes]  ADD  CONSTRAINT [FK_WelfareTypesAttachmentTypes_WelfareType_WelfareTypeID] FOREIGN KEY([WelfareTypeID])
REFERENCES [dbo].[WelfareType] ([WelfareTypeID])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[WelfareTypesAttachmentTypes] CHECK CONSTRAINT [FK_WelfareTypesAttachmentTypes_WelfareType_WelfareTypeID]
GO


GO
ALTER TABLE [dbo].[WelfareTypesAttachmentTypes]  ADD  CONSTRAINT [FK_WelfareTypesAttachmentTypes_AttachmentType_AttachmentTypeID] FOREIGN KEY([AttachmentTypeID])
REFERENCES [dbo].[AttachmentType] ([AttachmentTypeID])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[WelfareTypesAttachmentTypes] CHECK CONSTRAINT [FK_WelfareTypesAttachmentTypes_AttachmentType_AttachmentTypeID]
GO

