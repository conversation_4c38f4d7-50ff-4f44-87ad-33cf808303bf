CREATE TABLE [dbo].[WelfareRequestAttachment] (
    [WelfareRequestAttachmentId] BIGINT         IDENTITY (1, 1) NOT NULL,
    [FK_RequestId]               BIGINT         NOT NULL,
    [FK_AttachmentTypeId]        INT            NOT NULL,
    [AttachmentPath]             NVARCHAR (200) NOT NULL,
    CONSTRAINT [PK_RequestAttachments_1] PRIMARY KEY CLUSTERED ([WelfareRequestAttachmentId] ASC),
    CONSTRAINT [FK_WelfareRequestAttachment_AttachmentType_FK_AttachmentTypeId] FOREIGN KEY ([FK_AttachmentTypeId]) REFERENCES [dbo].[AttachmentType] ([AttachmentTypeID]) ON DELETE CASCADE,
    CONSTRAINT [FK_WelfareRequestAttachment_WelfareRequest_FK_RequestId] FOREIGN KEY ([FK_RequestId]) REFERENCES [dbo].[WelfareRequest] ([WelfareRequestId]) ON DELETE CASCADE
);




GO
/****** Object:  Index [IX_RequestAttachments_FK_AttachmentTypeId]    Script Date: 22/05/2025 1:18:15 PM ******/
/****** Object:  Index [IX_RequestAttachments_FK_AttachmentTypeId]    Script Date: 26/05/2025 4:30:09 PM ******/



GO
/****** Object:  Index [IX_RequestAttachments_FK_RequestId]    Script Date: 22/05/2025 1:18:15 PM ******/
/****** Object:  Index [IX_RequestAttachments_FK_RequestId]    Script Date: 26/05/2025 4:30:09 PM ******/

GO

GO


GO


GO

GO


GO
CREATE NONCLUSTERED INDEX [IX_WelfareRequestAttachment_FK_RequestId]
    ON [dbo].[WelfareRequestAttachment]([FK_RequestId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_WelfareRequestAttachment_FK_AttachmentTypeId]
    ON [dbo].[WelfareRequestAttachment]([FK_AttachmentTypeId] ASC);

