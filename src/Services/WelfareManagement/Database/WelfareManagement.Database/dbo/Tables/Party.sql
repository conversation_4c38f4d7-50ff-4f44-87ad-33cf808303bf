﻿CREATE TABLE [dbo].[Party](
	[PartyID] [bigint] NOT NULL,
	[FK_PartyTypeID] [int] NOT NULL,
	[Name] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_Party] PRIMARY KEY CLUSTERED 
(
	[PartyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
/****** Object:  Index [IX_Party_FK_PartyTypeID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Party_FK_PartyTypeID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Party_FK_PartyTypeID] ON [dbo].[Party]
(
	[FK_PartyTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[Party]  ADD  CONSTRAINT [FK_Party_PartyType_FK_PartyTypeID] FOREIGN KEY([FK_PartyTypeID])
REFERENCES [dbo].[PartyType] ([PartyTypeID])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[Party] CHECK CONSTRAINT [FK_Party_PartyType_FK_PartyTypeID]
GO

