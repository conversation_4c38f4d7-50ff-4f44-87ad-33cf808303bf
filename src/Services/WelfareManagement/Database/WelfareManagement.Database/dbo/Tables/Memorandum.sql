CREATE TABLE [dbo].[Memorandum] (
    [MemorandumId]             INT            IDENTITY (1, 1) NOT NULL,
    [Title]                    NVARCHAR (256) NOT NULL,
    [Notes]                    NVARCHAR (400) NULL,
    [FK_RequestType]           INT            NOT NULL,
    [UpdatedUserName]          NVARCHAR (200) NULL,
    [CreatedDate]              DATETIME2 (7)  NOT NULL,
    [CreatedByUserId]          VARCHAR (100)  NOT NULL,
    [CreatedByUserName]        NVARCHAR (200) NOT NULL,
    [UpdatedUserId]            VARCHAR (100)  NULL,
    [UpdatedDate]              DATETIME2 (7)  NULL,
    [MemorandumNo]             NVARCHAR (18)  DEFAULT (N'') NOT NULL,
    [MemorandumAttachmentPath] NVARCHAR (200) DEFAULT (N'') NOT NULL,
    CONSTRAINT [PK_Memorandum] PRIMARY KEY CLUSTERED ([MemorandumId] ASC),
    CONSTRAINT [FK_Memorandum_RequestType_FK_RequestType] FOREIGN KEY ([FK_RequestType]) REFERENCES [dbo].[RequestType] ([RequestTypeID]) ON DELETE CASCADE
);




GO
/****** Object:  Index [IX_Memorandum_FK_RequestType]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_Memorandum_FK_RequestType]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_Memorandum_FK_RequestType] ON [dbo].[Memorandum]
(
	[FK_RequestType] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User name of the user who last updated the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Memorandum', @level2type=N'COLUMN',@level2name=N'UpdatedUserName'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Date and time when the request was created' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Memorandum', @level2type=N'COLUMN',@level2name=N'CreatedDate'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User ID of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Memorandum', @level2type=N'COLUMN',@level2name=N'CreatedByUserId'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User name of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Memorandum', @level2type=N'COLUMN',@level2name=N'CreatedByUserName'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User ID of the user who last updated the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Memorandum', @level2type=N'COLUMN',@level2name=N'UpdatedUserId'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Date and time when the request record was last updated' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Memorandum', @level2type=N'COLUMN',@level2name=N'UpdatedDate'
GO

GO


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'path of memorandum Attachment', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Memorandum', @level2type = N'COLUMN', @level2name = N'MemorandumAttachmentPath';

