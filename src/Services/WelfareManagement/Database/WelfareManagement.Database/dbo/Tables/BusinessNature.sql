﻿CREATE TABLE [dbo].[BusinessNature](
	[BusinessNatureID] [int] NOT NULL,
	[Code] [nvarchar](30) NOT NULL,
	[Text] [nvarchar](250) NOT NULL,
	[Text2] [nvarchar](250) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsActive] [bit] NOT NULL,
 CONSTRAINT [PK_BusinessNature] PRIMARY KEY CLUSTERED 
(
	[BusinessNatureID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Table storing business natures' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BusinessNature'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Unique identifier for the business nature' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BusinessNature', @level2type=N'COLUMN',@level2name=N'BusinessNatureID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Code representing the business nature' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BusinessNature', @level2type=N'COLUMN',@level2name=N'Code'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'English text description of the business nature' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BusinessNature', @level2type=N'COLUMN',@level2name=N'Text'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Arabic text description of the business nature' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BusinessNature', @level2type=N'COLUMN',@level2name=N'Text2'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Indicates if the business nature is deleted' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BusinessNature', @level2type=N'COLUMN',@level2name=N'IsDeleted'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Indicates if the business nature is active' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BusinessNature', @level2type=N'COLUMN',@level2name=N'IsActive'
GO
ALTER TABLE [dbo].[BusinessNature] ADD  DEFAULT (CONVERT([bit],(1))) FOR [IsActive]