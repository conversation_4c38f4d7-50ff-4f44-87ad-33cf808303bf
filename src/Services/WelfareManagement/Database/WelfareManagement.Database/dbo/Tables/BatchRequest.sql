﻿CREATE TABLE [dbo].[BatchRequest] (
    [BatchRequestId]             INT             NOT NULL,
    [BatchN<PERSON>]                    NVARCHAR (18)   NOT NULL,
    [Title]                      NVARCHAR (128)  NOT NULL,
    [Notes]                      NVARCHAR (400)  NULL,
    [TotalAmount]                DECIMAL (12, 2) NULL,
    [UpdatedDate]                DATETIME2 (7)   NULL,
    [UpdatedUserName]            NVARCHAR (200)  NULL,
    [CreatedDate]                DATETIME2 (7)   NOT NULL,
    [CreatedByUserId]            VARCHAR (100)   NOT NULL,
    [CreatedByUserName]          NVARCHAR (200)  NOT NULL,
    [UpdatedUserId]              VARCHAR (100)   NULL,
    [BatchRequestAttachmentPath] NVARCHAR (200)  DEFAULT (N'') NOT NULL,
    CONSTRAINT [PK_BatchRequest] PRIMARY KEY CLUSTERED ([BatchRequestId] ASC)
);




GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Date and time when the request record was last updated' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequest', @level2type=N'COLUMN',@level2name=N'UpdatedDate'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User name of the user who last updated the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequest', @level2type=N'COLUMN',@level2name=N'UpdatedUserName'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Date and time when the request was created' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequest', @level2type=N'COLUMN',@level2name=N'CreatedDate'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User ID of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequest', @level2type=N'COLUMN',@level2name=N'CreatedByUserId'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User name of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequest', @level2type=N'COLUMN',@level2name=N'CreatedByUserName'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User ID of the user who last updated the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'BatchRequest', @level2type=N'COLUMN',@level2name=N'UpdatedUserId'
GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'path of Batch Request Attachment', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'BatchRequest', @level2type = N'COLUMN', @level2name = N'BatchRequestAttachmentPath';

