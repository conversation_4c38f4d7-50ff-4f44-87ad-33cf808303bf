﻿CREATE TABLE [dbo].[WelfareRequestStepConfiguration] (
    [WelfareRequestId] BIGINT         NOT NULL,
    [Id]               INT            IDENTITY (1, 1) NOT NULL,
    [Role]             NVARCHAR (100) NOT NULL,
    [ActionTypeID]     INT            NOT NULL,
    CONSTRAINT [PK_WelfareRequestStepConfiguration] PRIMARY KEY CLUSTERED ([WelfareRequestId] ASC, [Id] ASC),
    CONSTRAINT [FK_WelfareRequestStepConfiguration_WelfareRequest_WelfareRequestId] FOREIGN KEY ([WelfareRequestId]) REFERENCES [dbo].[WelfareRequest] ([WelfareRequestId]) ON DELETE CASCADE
);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Action type for this step', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'WelfareRequestStepConfiguration', @level2type = N'COLUMN', @level2name = N'ActionTypeID';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Role responsible for this step', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'WelfareRequestStepConfiguration', @level2type = N'COLUMN', @level2name = N'Role';

