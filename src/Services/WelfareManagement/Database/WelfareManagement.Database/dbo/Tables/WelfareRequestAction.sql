﻿CREATE TABLE [dbo].[WelfareRequestAction] (
    [WelfareRequestActionId]  BIGINT         IDENTITY (1, 1) NOT NULL,
    [CreatedByUserId]         VARCHAR (100)  NOT NULL,
    [FK_WorkflowReasonID]     INT            NULL,
    [FK_WelfareRequestID]     BIGINT         NOT NULL,
    [FK_WelfareRequestStepId] INT            NOT NULL,
    [FK_ActionTypeID]         INT            NOT NULL,
    [CreatedByUserName]       NVARCHAR (200) NOT NULL,
    [CreatedDate]             DATETIME2 (7)  NOT NULL,
    [Notes]                   NVARCHAR (255) NULL,
    CONSTRAINT [PK_WelfareRequestAction] PRIMARY KEY CLUSTERED ([WelfareRequestActionId] ASC),
    CONSTRAINT [FK_WelfareRequestAction_ActionType_FK_ActionTypeID] FOREIGN KEY ([FK_ActionTypeID]) REFERENCES [dbo].[ActionType] ([ActionTypeId]) ON DELETE CASCADE,
    CONSTRAINT [FK_WelfareRequestAction_WelfareRequest_FK_WelfareRequestID] FOREIGN KEY ([FK_WelfareRequestID]) REFERENCES [dbo].[WelfareRequest] ([WelfareRequestId]) ON DELETE CASCADE,
    CONSTRAINT [FK_WelfareRequestAction_WelfareRequestStep_FK_WelfareRequestStepId] FOREIGN KEY ([FK_WelfareRequestStepId]) REFERENCES [dbo].[WelfareRequestStep] ([WelfareRequestStepId]) ON DELETE CASCADE,
    CONSTRAINT [FK_WelfareRequestAction_WorkflowReason_FK_WorkflowReasonID] FOREIGN KEY ([FK_WorkflowReasonID]) REFERENCES [dbo].[WorkflowReason] ([WorkflowReasonId])
);




GO
/****** Object:  Index [IX_WelfareRequestAction_FK_ActionTypeID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_WelfareRequestAction_FK_ActionTypeID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_WelfareRequestAction_FK_ActionTypeID] ON [dbo].[WelfareRequestAction]
(
	[FK_ActionTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_WelfareRequestAction_FK_WelfareRequestID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_WelfareRequestAction_FK_WelfareRequestID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_WelfareRequestAction_FK_WelfareRequestID] ON [dbo].[WelfareRequestAction]
(
	[FK_WelfareRequestID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_WelfareRequestAction_FK_WelfareRequestStepId]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_WelfareRequestAction_FK_WelfareRequestStepId]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_WelfareRequestAction_FK_WelfareRequestStepId] ON [dbo].[WelfareRequestAction]
(
	[FK_WelfareRequestStepId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
/****** Object:  Index [IX_WelfareRequestAction_FK_WorkflowReasonID]    Script Date: 22/05/2025 1:18:14 PM ******/
/****** Object:  Index [IX_WelfareRequestAction_FK_WorkflowReasonID]    Script Date: 26/05/2025 4:30:09 PM ******/
CREATE NONCLUSTERED INDEX [IX_WelfareRequestAction_FK_WorkflowReasonID] ON [dbo].[WelfareRequestAction]
(
	[FK_WorkflowReasonID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User ID of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequestAction', @level2type=N'COLUMN',@level2name=N'CreatedByUserId'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Reason for workflow action, if applicable' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequestAction', @level2type=N'COLUMN',@level2name=N'FK_WorkflowReasonID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Parent request identifier' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequestAction', @level2type=N'COLUMN',@level2name=N'FK_WelfareRequestID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Current step in workflow for the action' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequestAction', @level2type=N'COLUMN',@level2name=N'FK_WelfareRequestStepId'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Type of action performed' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequestAction', @level2type=N'COLUMN',@level2name=N'FK_ActionTypeID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'User name of the user who created the request record' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequestAction', @level2type=N'COLUMN',@level2name=N'CreatedByUserName'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Date and time when the request was created' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WelfareRequestAction', @level2type=N'COLUMN',@level2name=N'CreatedDate'
GO

GO


GO


GO

GO


GO


GO

GO


GO


GO

GO


GO

