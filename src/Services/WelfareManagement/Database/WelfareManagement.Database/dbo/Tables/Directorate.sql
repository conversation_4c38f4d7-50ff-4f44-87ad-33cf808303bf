﻿CREATE TABLE [dbo].[Directorate](
	[DirectorateID] [int] NOT NULL,
	[Code] [nvarchar](30) NOT NULL,
	[Text] [nvarchar](50) NOT NULL,
	[Text2] [nvarchar](50) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsActive] [bit] NOT NULL,
	[FK_GovernorateID] [int] NOT NULL,
 CONSTRAINT [PK_Directorate] PRIMARY KEY CLUSTERED 
(
	[DirectorateID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Table storing directorates' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Directorate'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Unique identifier for the directorate' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Directorate', @level2type=N'COLUMN',@level2name=N'DirectorateID'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Code representing the directorate' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Directorate', @level2type=N'COLUMN',@level2name=N'Code'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'English text description of the directorate' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Directorate', @level2type=N'COLUMN',@level2name=N'Text'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Arabic text description of the directorate' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Directorate', @level2type=N'COLUMN',@level2name=N'Text2'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Indicates if the directorate is deleted' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Directorate', @level2type=N'COLUMN',@level2name=N'IsDeleted'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Indicates if the directorate is active' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Directorate', @level2type=N'COLUMN',@level2name=N'IsActive'


GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Foreign key to the Governorate table' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Directorate', @level2type=N'COLUMN',@level2name=N'FK_GovernorateID'
GO
ALTER TABLE [dbo].[Directorate] ADD  DEFAULT (CONVERT([bit],(1))) FOR [IsActive]