﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>WelfareManagement.Database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{7a6c9925-6a82-4d04-890c-752666b27589}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql150DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>WelfareManagement.Database</RootNamespace>
    <AssemblyName>WelfareManagement.Database</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="dbo\Scripts" />
    <Folder Include="Scripts\" />
    <Folder Include="dbo\ManualScripts" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\Labor.sql" />
    <Build Include="dbo\Tables\WelfareRequest.sql" />
    <Build Include="dbo\Tables\WelfareTypesAttachmentTypes.sql" />
    <Build Include="dbo\Tables\DisabilityWelfareRequest.sql" />
    <Build Include="dbo\Tables\SocialWelfareRequest.sql" />
    <Build Include="dbo\Tables\WelfareRequestAction.sql" />
    <Build Include="dbo\Tables\WelfareRequestAttachment.sql" />
    <Build Include="dbo\Tables\Beneficiary.sql" />
    <Build Include="dbo\Tables\ActionType.sql" />
    <Build Include="dbo\Tables\AttachmentType.sql" />
    <Build Include="dbo\Tables\BatchRequest.sql" />
    <Build Include="dbo\Tables\BatchRequestStep.sql" />
    <Build Include="dbo\Tables\BeneficiaryType.sql" />
    <Build Include="dbo\Tables\BusinessNature.sql" />
    <Build Include="dbo\Tables\Directorate.sql" />
    <Build Include="dbo\Tables\Gender.sql" />
    <Build Include="dbo\Tables\MaritalStatus.sql" />
    <Build Include="dbo\Tables\MedicalServiceProvider.sql" />
    <Build Include="dbo\Tables\NotificationReceiverType.sql" />
    <Build Include="dbo\Tables\Occupation.sql" />
    <Build Include="dbo\Tables\PartyType.sql" />
    <Build Include="dbo\Tables\RelativeRelationship.sql" />
    <Build Include="dbo\Tables\RelativeRelationType.sql" />
    <Build Include="dbo\Tables\RequesterRelevance.sql" />
    <Build Include="dbo\Tables\RequestStatus.sql" />
    <Build Include="dbo\Tables\RequestType.sql" />
    <Build Include="dbo\Tables\WelfareCategory.sql" />
    <Build Include="dbo\Tables\WelfareRequestStep.sql" />
    <Build Include="dbo\Tables\WorkflowReason.sql" />
    <Build Include="dbo\Tables\ActionTypeBatchRequestStep.sql" />
    <Build Include="dbo\Tables\MedicalWelfareRequest.sql" />
    <Build Include="dbo\Tables\Party.sql" />
    <Build Include="dbo\Tables\Memorandum.sql" />
    <Build Include="dbo\Tables\WelfareType.sql" />
    <Build Include="dbo\Tables\ActionTypeWelfareRequestStep.sql" />
    <Build Include="dbo\Tables\BatchRequestAction.sql" />
    <None Include="dbo\Scripts\ActionType.sql" />
    <None Include="dbo\Scripts\AttachmentType.sql" />
    <None Include="dbo\Scripts\BatchRequestStep.sql" />
    <None Include="dbo\Scripts\BeneficiaryType.sql" />
    <None Include="dbo\Scripts\BusinessNature.sql" />
    <None Include="dbo\Scripts\Directorate.sql" />
    <None Include="dbo\Scripts\Gender.sql" />
    <None Include="dbo\Scripts\MaritalStatus.sql" />
    <None Include="dbo\Scripts\MedicalServiceProvider.sql" />
    <None Include="dbo\Scripts\NotificationReceiverType.sql" />
    <None Include="dbo\Scripts\Occupation.sql" />
    <None Include="dbo\Scripts\PartyType.sql" />
    <None Include="dbo\Scripts\RelativeRelationship.sql" />
    <None Include="dbo\Scripts\RelativeRelationType.sql" />
    <None Include="dbo\Scripts\RequesterRelevance.sql" />
    <None Include="dbo\Scripts\RequestStatus.sql" />
    <None Include="dbo\Scripts\RequestType.sql" />
    <None Include="dbo\Scripts\WelfareCategory.sql" />
    <None Include="dbo\Scripts\WelfareRequestStep.sql" />
    <None Include="dbo\Scripts\WelfareType.sql" />
    <None Include="dbo\Scripts\WorkflowReason.sql" />
    <Build Include="dbo\Tables\__EFMigrationsHistory.sql" />
    <None Include="Scripts\ScriptsIgnoredOnImport.sql" />
    <Build Include="dbo\Tables\WelfareRequestStepConfiguration.sql" />
    <Build Include="dbo\Tables\WelfareRequestStatus.sql" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="dbo\Scripts\Script.PostDeployment.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="dbo\Scripts\WelfareTypesAttachmentTypes.sql" />
    <None Include="dbo\ManualScripts\Script1.sql" />
  </ItemGroup>
</Project>