﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using S3.Core.Infrastructures.DependencyInjection;
using S3.MoL.WelfareManagement.Application.Models;
using S3.MoL.WelfareManagement.Application.Services.Persistence;
using S3.MoL.WelfareManagement.Infrastructure.Services.Audit;
using S3.MoL.WelfareManagement.Infrastructure.Services.Persistence;
using System.Reflection;

namespace S3.MoL.WelfareManagement.Infrastructure.DependencyInjection
{
    public static class IoC
    {
        public static IServiceCollection AddInfrastructure(
            this IServiceCollection services,
            WelfareManagementConfiguration welfareManagementConfiguration,
            Assembly consumersAssembly)
        {
            services.AddPersistence(welfareManagementConfiguration.ConnectionString);

            services.AddScoped<IAuditService, AuditService>();

            services.AddServiceBus(
                welfareManagementConfiguration.Messaging,
                consumersAssembly);
            services.AddScoped<IAuditService, AuditService>();

            return services;
        }

        private static IServiceCollection AddPersistence(this IServiceCollection services, string connectionString)
        {
            services.AddDbContext<IWelfareManagementDbContext, WelfareManagementDbContext>(x => x.UseSqlServer(connectionString));
            return services;
        }
    }
}
