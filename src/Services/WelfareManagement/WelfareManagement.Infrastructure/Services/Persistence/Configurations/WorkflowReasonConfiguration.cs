﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using S3.MoL.WelfareManagement.Domain.Enums;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class WorkflowReasonConfiguration : IEntityTypeConfiguration<WorkflowReason>
    {
        public void Configure(EntityTypeBuilder<WorkflowReason> entity)
        {
            entity.ToTable("WorkflowReason");

            entity.Property(e => e.WorkflowReasonId).ValueGeneratedNever();
            entity.Property(e => e.Code).HasMaxLength(50);
            entity.Property(e => e.Text).HasMaxLength(50);
            entity.Property(e => e.Text2).HasMaxLength(50);
            entity.Property(e => e.RequestType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasConversion(
                    v => v.ToString(), // Enum to string for storage
                    v => Enum.Parse<RequestTypes>(v,  true) // String to enum on read
                );

            entity.Property(e => e.IsDeleted).HasDefaultValue(false);
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<WorkflowReason> entity);
    }
}
