﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class WelfareCategoryConfiguration : IEntityTypeConfiguration<WelfareCategory>
    {
        public void Configure(EntityTypeBuilder<WelfareCategory> entity)
        {
            entity.ToTable("WelfareCategory", tb => tb.HasComment("Monetary or "));

            entity.Property(e => e.Code).HasMaxLength(30);
            entity.Property(e => e.Text).HasMaxLength(50);
            entity.Property(e => e.Text2).HasMaxLength(50);

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<WelfareCategory> entity);
    }
}
