﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class RequestTypeConfiguration : IEntityTypeConfiguration<RequestType>
    {
        public void Configure(EntityTypeBuilder<RequestType> entity)
        {
            entity.ToTable("RequestType");

            entity.Property(e => e.RequestTypeId)
                .ValueGeneratedNever()
                .HasComment("Unique identifier for each request type")
                .HasColumnName("RequestTypeID");
            entity.Property(e => e.Code)
                .HasMaxLength(30)
                .HasComment("Code representing the request type");
            entity.Property(e => e.Text)
                .HasMaxLength(50)
                .HasComment("English text description of the request type");
            entity.Property(e => e.Text2)
                .HasMaxLength(50)
                .HasComment("Arabic text description of the request type");
            entity.Property(e => e.IsMemorandum).IsRequired();

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<RequestType> entity);
    }
}
