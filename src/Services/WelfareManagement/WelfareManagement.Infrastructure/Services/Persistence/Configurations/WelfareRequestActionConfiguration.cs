﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class WelfareRequestActionConfiguration : IEntityTypeConfiguration<WelfareRequestAction>
{
    public void Configure(EntityTypeBuilder<WelfareRequestAction> entity)
    {
        entity.ToTable("WelfareRequestAction");

        entity.Property(e => e.CreatedByUserId)
            .HasMaxLength(100)
            .IsUnicode(false)
            .HasComment("User ID of the user who created the request record");
        entity.Property(e => e.CreatedByUserName)
            .HasMaxLength(200)
            .HasComment("User name of the user who created the request record");
        entity.Property(e => e.CreatedDate).HasComment("Date and time when the request was created");
        entity.Property(e => e.ActionTypeId)
            .HasComment("Type of action performed")
            .HasColumnName("FK_ActionTypeID");
        entity.Property(e => e.WelfareRequestId)
            .HasComment("Parent request identifier")
            .HasColumnName("FK_WelfareRequestID");
        entity.Property(e => e.WelfareRequestStepId)
            .HasComment("Current step in workflow for the action")
            .HasColumnName("FK_WelfareRequestStepId");
        entity.Property(e => e.WorkflowReasonId)
            .HasComment("Reason for workflow action, if applicable")
            .HasColumnName("FK_WorkflowReasonID");
        entity.Property(x => x.Notes).HasMaxLength(255);

        entity.HasOne(d => d.ActionType)
            .WithMany(
                p => p.WelfareRequestActions
            )
            .HasForeignKey(d => d.ActionTypeId)
            .OnDelete(DeleteBehavior.Cascade)
            ;
        entity.HasOne(d => d.WelfareRequest)
            .WithMany(
                p => p.WelfareRequestActions
            )
            .HasForeignKey(d => d.WelfareRequestId)
            .OnDelete(DeleteBehavior.Cascade)
            ;
        entity.HasOne(d => d.WelfareRequestStep)
            .WithMany(
                p => p.WelfareRequestActions
            )
            .HasForeignKey(d => d.WelfareRequestStepId)
            .OnDelete(DeleteBehavior.Cascade)
            ;
        entity.HasOne(d => d.WorkflowReason)
            .WithMany(
                p => p.WelfareRequestActions
            )
            .HasForeignKey(d => d.WorkflowReasonId)
            ;
        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<WelfareRequestAction> entity);
}
