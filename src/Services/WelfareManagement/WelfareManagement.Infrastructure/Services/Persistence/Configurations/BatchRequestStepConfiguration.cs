﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class BatchRequestStepConfiguration : IEntityTypeConfiguration<BatchRequestStep>
    {
        public void Configure(EntityTypeBuilder<BatchRequestStep> entity)
        {
            entity.ToTable("BatchRequestStep");

            entity.Property(e => e.BatchRequestStepId).ValueGeneratedNever();
            entity.Property(e => e.Code)
                .HasMaxLength(50)
                .HasComment("Code representing the request step");
            entity.Property(e => e.Text)
                .HasMaxLength(50)
                .HasComment("English text description of the request step");
            entity.Property(e => e.Text2)
                .HasMaxLength(50)
                .HasComment("Arabic text description of the request step");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<BatchRequestStep> entity);
    }
}
