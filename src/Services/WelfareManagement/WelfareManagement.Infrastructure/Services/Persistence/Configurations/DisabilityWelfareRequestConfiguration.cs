﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class DisabilityWelfareRequestConfiguration : IEntityTypeConfiguration<DisabilityWelfareRequest>
{
    public void Configure(EntityTypeBuilder<DisabilityWelfareRequest> entity)
    {
        //entity.HasKey(e => e.RequestId);

        entity.ToTable("DisabilityWelfareRequest");

        entity.Property(e => e.DisabilityRatio).HasColumnType("decimal(2, 0)");
        entity.Property(e => e.Description).HasMaxLength(255);

        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<DisabilityWelfareRequest> entity);
}
