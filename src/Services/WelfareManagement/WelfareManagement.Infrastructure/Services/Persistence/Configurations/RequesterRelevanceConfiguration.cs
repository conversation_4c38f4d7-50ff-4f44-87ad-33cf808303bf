﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class RequesterRelevanceConfiguration : IEntityTypeConfiguration<RequesterRelevance>
    {
        public void Configure(EntityTypeBuilder<RequesterRelevance> entity)
        {
            entity.HasKey(e => e.RequesterRelevantId).HasName("PK_RequesterRelevant");

            entity.ToTable("RequesterRelevance", tb => tb.<PERSON><PERSON><PERSON><PERSON>("Labor or Medical provider or Other"));

            entity.Property(e => e.RequesterRelevantId)
                .ValueGeneratedNever()
                .HasColumnName("RequesterRelevantID");
            entity.Property(e => e.Code).HasMaxLength(30);
            entity.Property(e => e.Text).HasMaxLength(50);
            entity.Property(e => e.Text2).HasMaxLength(50);

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<RequesterRelevance> entity);
    }
}
