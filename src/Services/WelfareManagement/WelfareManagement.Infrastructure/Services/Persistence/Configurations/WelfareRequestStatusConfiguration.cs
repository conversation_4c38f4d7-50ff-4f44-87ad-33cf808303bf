﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class WelfareRequestStatusConfiguration : IEntityTypeConfiguration<WelfareRequestStatus>
{
    public void Configure(EntityTypeBuilder<WelfareRequestStatus> entity)
    {
        entity.ToTable("WelfareRequestStatus");

        entity.HasKey(e => e.WelfareRequestStatusId);
        entity.Property(e => e.WelfareRequestStatusId)
            .ValueGeneratedNever()
            .HasComment("Unique identifier for each Welfare request status record")
            .HasColumnName("RequestStatusID");
        entity.Property(e => e.Code)
            .HasMaxLength(30)
            .HasComment("Code representing the Welfare request status");
        entity.Property(e => e.Text)
            .HasMaxLength(50)
            .HasComment("English text description of the Welfare request status");
        entity.Property(e => e.Text2)
            .HasMaxLength(50)
            .HasComment("Arabic text description of the Welfare request status");

        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<WelfareRequestStatus> entity);
}
