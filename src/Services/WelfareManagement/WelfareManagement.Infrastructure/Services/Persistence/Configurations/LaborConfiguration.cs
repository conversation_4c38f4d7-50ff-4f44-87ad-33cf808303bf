﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class LaborConfiguration : IEntityTypeConfiguration<Labor>
{
    public void Configure(EntityTypeBuilder<Labor> entity)
    {
        entity.ToTable("Labor");

        entity.HasIndex(e => e.GenderId, "IX_Labor_FK_GenderID");

        entity.HasIndex(e => e.LastBusinessNatureId, "IX_Labor_FK_LastBusinessNatureID");

        entity.HasIndex(e => e.LastDirectorateId, "IX_Labor_FK_LastDirectorateID");

        entity.HasIndex(e => e.LastExecutionPartyId, "IX_Labor_FK_LastExecutionPartyID");

        entity.HasIndex(e => e.MaritalStatusId, "IX_Labor_FK_MaritalStatusID");

        entity.HasIndex(e => e.OccupationId, "IX_Labor_FK_OccupationID");

        entity.Property(e => e.LaborId)
            .ValueGeneratedNever()
            .HasColumnName("LaborID");
        entity.Property(e => e.GenderId).HasColumnName("FK_GenderID");
        entity.Property(e => e.LastBusinessNatureId).HasColumnName("FK_LastBusinessNatureID");
        entity.Property(e => e.LastDirectorateId).HasColumnName("FK_LastDirectorateID");
        entity.Property(e => e.LastExecutionPartyId).HasColumnName("FK_LastExecutionPartyID");
        entity.Property(e => e.MaritalStatusId).HasColumnName("FK_MaritalStatusID");
        entity.Property(e => e.OccupationId).HasColumnName("FK_OccupationID");
        entity.Property(e => e.FullName).HasMaxLength(200);
        entity.Property(e => e.InsuranceNo)
            .HasMaxLength(9)
            .IsUnicode(false)
            .IsFixedLength();
        entity.Property(e => e.IsBeneficiary)
            .HasComputedColumnSql(
                "CASE WHEN [DeathDate] IS NOT NULL OR [HasFullDisability] = 1 THEN CAST(0 AS bit) ELSE CAST(1 AS bit) END",
                stored: true
            )
            .HasComment("the worker's status must be Beneficiary or  Not Beneficiary, default is Beneficiary(1)");
        entity.Property(e => e.MobileNo)
            .HasMaxLength(13)
            .IsUnicode(false)
            .IsFixedLength();
        entity.Property(e => e.NationalId)
            .HasMaxLength(14)
            .IsUnicode(false)
            .IsFixedLength()
            .HasColumnName("NationalID");
        entity.Property(e => e.RegistrationNo)
            .HasMaxLength(20)
            .IsUnicode(false);
        entity.Property(e => e.UpdatedDate).HasComment("Date and time when the request record was last updated");
        entity.Property(e => e.UpdatedUserId)
            .HasMaxLength(100)
            .IsUnicode(false)
            .HasComment("User ID of the user who last updated the request record");
        entity.Property(e => e.UpdatedUserName)
            .HasMaxLength(200)
            .HasComment("User name of the user who last updated the request record");
        entity.Property(e => e.HasFullDisability).HasDefaultValue(false);

        entity.HasOne(d => d.Gender)
            .WithMany(
                p => p.Labors
            )
            .HasForeignKey(d => d.GenderId)
            ;
        entity.HasOne(d => d.LastBusinessNature)
            .WithMany(
                p => p.Labors
            )
            .HasForeignKey(d => d.LastBusinessNatureId)
            ;
        entity.HasOne(d => d.LastDirectorate)
            .WithMany(
                p => p.Labors
            )
            .HasForeignKey(d => d.LastDirectorateId)
            ;
        entity.HasOne(d => d.LastExecutionParty)
            .WithMany(
                p => p.Labors
            )
            .HasForeignKey(d => d.LastExecutionPartyId)
            ;
        entity.HasOne(d => d.MaritalStatus)
            .WithMany(
                p => p.Labors
            )
            .HasForeignKey(d => d.MaritalStatusId)
            ;
        entity.HasOne(d => d.Occupation)
            .WithMany(
                p => p.Labors
            )
            .HasForeignKey(d => d.OccupationId)
            ;
        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<Labor> entity);
}
