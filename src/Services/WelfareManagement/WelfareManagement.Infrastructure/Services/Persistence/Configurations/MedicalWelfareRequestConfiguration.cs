﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class MedicalWelfareRequestConfiguration : IEntityTypeConfiguration<MedicalWelfareRequest>
    {
        public void Configure(EntityTypeBuilder<MedicalWelfareRequest> entity)
        {
            entity.HasKey(e => e.RequestId);

            entity.ToTable("MedicalWelfareRequest");

            entity.HasIndex(e => e.BeneficiaryTypeId, "IX_MedicalWelfareRequest_FK_BeneficiaryTypeId");

            entity.HasIndex(e => e.MedicalServiceProviderId, "IX_MedicalWelfareRequest_FK_MedicalServiceProviderId");

            entity.Property(e => e.RequestId).ValueGeneratedNever();
            entity.Property(e => e.BeneficiaryIban)
                .HasMaxLength(120)
                .HasColumnName("BeneficiaryIBAN");
            entity.Property(e => e.BeneficiaryName).HasMaxLength(120);
            entity.Property(e => e.BeneficiaryNid).HasColumnName("BeneficiaryNId");
            entity.Property(e => e.Description).HasMaxLength(250);
            entity.Property(e => e.BeneficiaryTypeId).HasColumnName("FK_BeneficiaryTypeId");
            entity.Property(e => e.MedicalServiceProviderId).HasColumnName("FK_MedicalServiceProviderId");

            entity.HasOne(d => d.BeneficiaryType)
                .WithMany(
                    p => p.MedicalWelfareRequests
                ).HasForeignKey(d => d.BeneficiaryTypeId);

            entity.HasOne(d => d.MedicalServiceProvider)
                .WithMany(
                    p => p.MedicalWelfareRequests
                ).HasForeignKey(d => d.MedicalServiceProviderId);

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<MedicalWelfareRequest> entity);
    }
}
