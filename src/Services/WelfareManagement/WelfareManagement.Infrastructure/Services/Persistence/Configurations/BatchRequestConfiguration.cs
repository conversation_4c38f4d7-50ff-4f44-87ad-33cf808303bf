﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class BatchRequestConfiguration : IEntityTypeConfiguration<BatchRequest>
    {
        public void Configure(EntityTypeBuilder<BatchRequest> entity)
        {
            entity.ToTable("BatchRequest");

            entity.Property(e => e.BatchRequestId).ValueGeneratedNever();
            entity.Property(e => e.BatchNo).HasMaxLength(18);
            entity.Property(e => e.CreatedByUserId)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasComment("User ID of the user who created the request record");
            entity.Property(e => e.CreatedByUserName)
                .HasMaxLength(200)
                .HasComment("User name of the user who created the request record");
            entity.Property(e => e.CreatedDate).HasComment("Date and time when the request was created");
            entity.Property(e => e.Notes).HasMaxLength(400);
            entity.Property(e => e.Title).HasMaxLength(128);
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(12, 2)");
            entity.Property(e => e.UpdatedDate).HasComment("Date and time when the request record was last updated");
            entity.Property(e => e.UpdatedUserId)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasComment("User ID of the user who last updated the request record");
            entity.Property(e => e.UpdatedUserName)
                .HasMaxLength(200)
                .HasComment("User name of the user who last updated the request record");
            entity.Property(e => e.BatchRequestAttachmentPath)
                .HasMaxLength(200)
                .HasComment("path of Batch Request Attachment");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<BatchRequest> entity);
    }
}
