﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class OccupationConfiguration : IEntityTypeConfiguration<Occupation>
    {
        public void Configure(EntityTypeBuilder<Occupation> entity)
        {
            entity.ToTable("Occupation");

            entity.Property(e => e.OccupationId)
                .ValueGeneratedNever()
                .HasComment("Unique identifier for each occupation record")
                .HasColumnName("OccupationID");
            entity.Property(e => e.Code)
                .HasMaxLength(30)
                .HasComment("Code representing the occupation");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Text)
                .HasMaxLength(50)
                .HasComment("English text description of the occupation");
            entity.Property(e => e.Text2)
                .HasMaxLength(50)
                .HasComment("Arabic text description of the occupation");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<Occupation> entity);
    }
}
