﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class WelfareRequestConfiguration : IEntityTypeConfiguration<WelfareRequest>
{
    public void Configure(EntityTypeBuilder<WelfareRequest> entity)
    {
        entity.HasKey(e => e.WelfareRequestId);

        entity.ToTable("WelfareRequest");

        entity.HasIndex(e => e.DirectorateId, "IX_Request_FK_DirectorateID");

        entity.HasIndex(e => e.RequestStatusId, "IX_Request_FK_StatusID");

        entity.HasIndex(e => e.WelfareTypeId, "IX_Request_FK_WelfareTypeID");

        entity.Property(e => e.CreatedByUserId)
            .HasMaxLength(100)
            .IsUnicode(false)
            .HasComment("User ID of the user who created the request record");
        entity.Property(e => e.CreatedByUserName)
            .HasMaxLength(200)
            .HasComment("User name of the user who created the request record");
        entity.Property(e => e.CreatedDate).HasComment("Date and time when the request was created");
        entity.Property(e => e.DueAmount).HasColumnType("decimal(8, 2)");
        entity.Property(e => e.BatchId).HasColumnName("FK_BatchId");
        entity.Property(e => e.DirectorateId)
            .HasComment("Identifier for the directorate associated with the request")
            .HasColumnName("FK_DirectorateID");
        entity.Property(e => e.LaborId).HasColumnName("FK_LaborId");
        entity.Property(e => e.MemorandumId).HasColumnName("FK_MemorandumId");
        entity.Property(e => e.RequestStatusId)
            .HasComment("Identifier for the current status of a current workflow")
            .HasColumnName("FK_RequestStatusID");
        entity.Property(e => e.WelfareRequestStatusId)
            .HasComment("Identifier for the current status of a current workflow")
            .HasColumnName("FK_WelfareRequestStatusID");
        entity.Property(e => e.WelfareTypeId)
            .HasComment("Identifier for the type of request")
            .HasColumnName("FK_WelfareTypeID");
        entity.Property(e => e.LaborMobileNo).HasMaxLength(12);
        entity.Property(e => e.Notes).HasMaxLength(256);
        entity.Property(e => e.RequestNo).HasMaxLength(18);
        entity.Property(e => e.UpdatedDate).HasComment("Date and time when the request record was last updated");
        entity.Property(e => e.UpdatedUserId)
            .HasMaxLength(100)
            .IsUnicode(false)
            .HasComment("User ID of the user who last updated the request record");
        entity.Property(e => e.UpdatedUserName)
            .HasMaxLength(200)
            .HasComment("User name of the user who last updated the request record");
        entity.Property(e => e.Version)
            .IsRowVersion()
            .IsConcurrencyToken()
            .HasComment("Timestamp for version control of the request record");

        entity.HasOne(d => d.Batch)
            .WithMany(
                p => p.WelfareRequests
            )
            .HasForeignKey(d => d.BatchId)
            ;
        entity.HasOne(d => d.Directorate)
            .WithMany(
                p => p.WelfareRequests
            )
            .HasForeignKey(d => d.DirectorateId)
            ;
        entity.HasOne(d => d.Memorandum)
            .WithMany(
                p => p.WelfareRequests
            )
            .HasForeignKey(d => d.MemorandumId)
            ;
        entity.HasOne(d => d.RequestStatus)
            .WithMany(
                p => p.WelfareRequests
            )
            .HasForeignKey(d => d.RequestStatusId)
            ;
        entity.HasOne(d => d.WelfareRequestStatus)
            .WithMany(
                p => p.WelfareRequests
            )
            .HasForeignKey(d => d.WelfareRequestStatusId)
            ;
        entity.HasOne(d => d.WelfareType)
            .WithMany(
                p => p.WelfareRequests
            )
            .HasForeignKey(d => d.WelfareTypeId)
            ;
        entity.OwnsMany<StepConfiguration>(
            e => e.StepConfigurations,
            b =>
            {
                b.ToTable("WelfareRequestStepConfiguration");
                b.Property(e => e.Role).HasMaxLength(100)
                    .HasComment("Role responsible for this step");

                b.Property(e => e.ActionTypeID)
                    .IsRequired()
                    .HasComment("Action type for this step");
            }
        );
        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<WelfareRequest> entity);
}
