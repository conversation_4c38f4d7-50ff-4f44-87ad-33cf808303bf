﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class MedicalServiceProviderConfiguration : IEntityTypeConfiguration<MedicalServiceProvider>
    {
        public void Configure(EntityTypeBuilder<MedicalServiceProvider> entity)
        {
            entity.ToTable("MedicalServiceProvider");

            entity.Property(e => e.MedicalServiceProviderId).ValueGeneratedNever();
            entity.Property(e => e.Code).HasMaxLength(30);
            entity.Property(e => e.Iban)
                .HasMaxLength(120)
                .HasColumnName("IBAN");
            entity.Property(e => e.Text).HasMaxLength(50);
            entity.Property(e => e.Text2).HasMaxLength(50);

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<MedicalServiceProvider> entity);
    }
}
