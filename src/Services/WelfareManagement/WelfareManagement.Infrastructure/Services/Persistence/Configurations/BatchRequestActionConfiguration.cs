﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations
{
    public partial class BatchRequestActionConfiguration : IEntityTypeConfiguration<BatchRequestAction>
    {
        public void Configure(EntityTypeBuilder<BatchRequestAction> entity)
        {
            entity.ToTable("BatchRequestAction");

            entity.Property(e => e.CreatedByUserId)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasComment("User ID of the user who created the request record");
            entity.Property(e => e.CreatedByUserName)
                .HasMaxLength(200)
                .HasComment("User name of the user who created the request record");
            entity.Property(e => e.CreatedDate).HasComment("Date and time when the request was created");
            entity.Property(e => e.ActionTypeId)
                .HasComment("Type of action performed")
                .HasColumnName("FK_ActionTypeID");
            entity.Property(e => e.BatchRequestId)
                .HasComment("Parent request identifier")
                .HasColumnName("FK_BatchRequestID");
            entity.Property(e => e.BatchRequestStepId)
                .HasComment("Current step in workflow for the action")
                .HasColumnName("FK_BatchRequestStepId");
            entity.Property(e => e.WorkflowReasonId)
                .HasComment("Reason for workflow action, if applicable")
                .HasColumnName("FK_WorkflowReasonID");

            entity.HasOne(d => d.ActionType)
                .WithMany(
                    p => p.BatchRequestActions
                )
                .HasForeignKey(d => d.ActionTypeId)
                .OnDelete(DeleteBehavior.Cascade)
                ;
            entity.HasOne(d => d.BatchRequest)
                .WithMany(
                    p => p.BatchRequestActions
                )
                .HasForeignKey(d => d.BatchRequestId)
                .OnDelete(DeleteBehavior.Cascade)
                ;
            entity.HasOne(d => d.BatchRequestStep)
                .WithMany(
                    p => p.BatchRequestActions
                )
                .HasForeignKey(d => d.BatchRequestStepId)
                .OnDelete(DeleteBehavior.Cascade)
                ;
            entity.HasOne(d => d.WorkflowReason)
                .WithMany(
                    p => p.BatchRequestActions
                )
                .HasForeignKey(d => d.WorkflowReasonId)
                ;
            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<BatchRequestAction> entity);
    }
}
