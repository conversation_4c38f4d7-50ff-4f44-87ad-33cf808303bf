﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;


#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class WelfareRequestAttachmentConfiguration : IEntityTypeConfiguration<WelfareRequestAttachment>
{
    public void Configure(EntityTypeBuilder<WelfareRequestAttachment> entity)
    {
        entity.HasKey(e => e.WelfareRequestAttachmentId).HasName("PK_RequestAttachments_1");

        entity.ToTable("WelfareRequestAttachment");

        entity.Property(e => e.AttachmentPath).HasMaxLength(200);
        entity.Property(e => e.AttachmentTypeId).HasColumnName("FK_AttachmentTypeId");
        entity.Property(e => e.WelfareRequestId).HasColumnName("FK_RequestId");

        entity.HasOne(d => d.AttachmentType)
            .WithMany(
                p => p.WelfareRequestAttachments
            )
            .HasForeignKey(d => d.AttachmentTypeId)
            .OnDelete(DeleteBehavior.Cascade)
            ;
        entity.HasOne(d => d.WelfareRequest)
            .WithMany(
                p => p.WelfareRequestAttachments
            )
            .HasForeignKey(d => d.WelfareRequestId)
            .OnDelete(DeleteBehavior.Cascade)
            ;
        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<WelfareRequestAttachment> entity);
}
