﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;

#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class MemorandumConfiguration : IEntityTypeConfiguration<Memorandum>
{
    public void Configure(EntityTypeBuilder<Memorandum> entity)
    {
        entity.ToTable("Memorandum");

        entity.Property(e => e.CreatedByUserId)
            .HasMaxLength(100)
            .IsUnicode(false)
            .HasComment("User ID of the user who created the request record");
        entity.Property(e => e.CreatedByUserName)
            .HasMaxLength(200)
            .HasComment("User name of the user who created the request record");
        entity.Property(e => e.CreatedDate).HasComment("Date and time when the request was created");
        entity.Property(e => e.RequestType).HasColumnName("FK_RequestType");
        entity.Property(e => e.Notes).HasMaxLength(400);
        entity.Property(e => e.Title).HasMaxLength(256);
        entity.Property(e => e.UpdatedDate).HasComment("Date and time when the request record was last updated");
        entity.Property(e => e.UpdatedUserId)
            .HasMaxLength(100)
            .IsUnicode(false)
            .HasComment("User ID of the user who last updated the request record");
        entity.Property(e => e.UpdatedUserName)
            .HasMaxLength(200)
            .HasComment("User name of the user who last updated the request record");
        entity.Property(e => e.MemorandumNo)
            .HasMaxLength(18);
        entity.Property(e => e.MemorandumAttachmentPath)
            .HasMaxLength(200)
            .HasComment("path of memorandum Attachment");

        entity.HasOne(d => d.RequestTypeNavigation)
            .WithMany(
                p => p.Memoranda
            )
            .HasForeignKey(d => d.RequestType)
            .OnDelete(DeleteBehavior.Cascade)
            ;
        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<Memorandum> entity);
}
