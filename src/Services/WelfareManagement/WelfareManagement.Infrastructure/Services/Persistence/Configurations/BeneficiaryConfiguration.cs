﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class BeneficiaryConfiguration : IEntityTypeConfiguration<Beneficiary>
{
    public void Configure(EntityTypeBuilder<Beneficiary> entity)
    {
        entity.ToTable("Beneficiary");

        entity.HasIndex(e => e.RelativeRelationTypeId, "IX_Beneficiary_FK_RelativeRelationTypeID");

        entity.HasIndex(e => e.SocialWelfareRequestId, "IX_Beneficiary_FK_SocialWelfareRequestId");

        entity.Property(e => e.BeneficiaryId)
            .HasComment("Unique identifier for Beneficiary")
            .HasColumnName("BeneficiaryID");
        entity.Property(e => e.Amount)
            .HasComment("Amount to be disbursed for beneficiary")
            .HasColumnType("decimal(8, 2)");
        entity.Property(e => e.BeneficiaryName)
            .HasMaxLength(120)
            .IsUnicode()
            .HasComment("Beneficiary name");
        entity.Property(e => e.LaborId)
            .HasComment("Unique identifier for labor associated with request")
            .HasColumnName("FK_LaborID");
        entity.Property(e => e.RelativeRelationTypeId)
            .HasComment("Relationship to dead employee")
            .HasColumnName("FK_RelativeRelationTypeID");
        entity.Property(e => e.SocialWelfareRequestId)
            .HasComment("Identifier of social request if applicable")
            .HasColumnName("FK_SocialWelfareRequestId");
        entity.Property(e => e.Iban)
            .HasMaxLength(120)
            .HasColumnName("IBAN");
        entity.Property(e => e.NationalId)
            .HasMaxLength(14)
            .IsUnicode(false)
            .IsFixedLength()
            .HasComment("National identification number of the labor")
            .HasColumnName("NationalID");

        entity.HasOne(d => d.RelativeRelationType)
            .WithMany(
                p => p.Beneficiaries
            ).HasForeignKey(d => d.RelativeRelationTypeId);

        entity.HasOne(d => d.SocialWelfareRequest)
            .WithMany(
                p => p.Beneficiaries
            ).HasForeignKey(d => d.SocialWelfareRequestId);

        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<Beneficiary> entity);
}
