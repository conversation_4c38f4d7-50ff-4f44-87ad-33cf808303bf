﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;



#nullable disable

namespace S3.MoL.WelfareManagement.Domain.Data.Configurations;

public partial class SocialWelfareRequestConfiguration : IEntityTypeConfiguration<SocialWelfareRequest>
{
    public void Configure(EntityTypeBuilder<SocialWelfareRequest> entity)
    {
        //entity.HasKey(e => e.RequestId);

        entity.ToTable("SocialWelfareRequest");

        entity.HasIndex(e => e.RelativeRelationshipId, "IX_SocialWelfareRequest_FK_RelativeRelationshipId");

        entity.HasIndex(e => e.RequesterRelevanceId, "IX_SocialWelfareRequest_FK_RequesterRelevanceId");

        entity.Property(e => e.RelativeRelationshipId).HasColumnName("FK_RelativeRelationshipId");
        entity.Property(e => e.RequesterRelevanceId).HasColumnName("FK_RequesterRelevanceId");
        entity.Property(e => e.NationalId)
            .HasMaxLength(14)
            .IsUnicode(false)
            .IsFixedLength();
        entity.Property(e => e.RequesterName).HasMaxLength(60);

        entity.HasOne(d => d.RelativeRelationship)
            .WithMany(
                p => p.SocialWelfareRequests
            ).HasForeignKey(d => d.RelativeRelationshipId);

        entity.HasOne(d => d.RequesterRelevance)
            .WithMany(
                p => p.SocialWelfareRequests
            ).HasForeignKey(d => d.RequesterRelevanceId);

        OnConfigurePartial(entity);
    }

    partial void OnConfigurePartial(EntityTypeBuilder<SocialWelfareRequest> entity);
}
