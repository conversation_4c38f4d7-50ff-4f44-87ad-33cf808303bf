﻿using MassTransit;
using S3.MoL.Audit.Contracts.Messages;

namespace S3.MoL.WelfareManagement.Infrastructure.Services.Audit
{
    public sealed class AuditService : IAuditService
    {
        private readonly IBus _bus;

        public AuditService(IBus bus)
        {
            _bus = bus;
        }
        public Task Audit(AuditLogMessage auditLogMessage)
        {
            return _bus.Publish(auditLogMessage);
        }
    }
}
