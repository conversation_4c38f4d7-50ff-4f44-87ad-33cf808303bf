﻿namespace S3.MoL.WelfareManagement.Api
{
    internal static class StartupHelpers
    {
        public static IServiceCollection HandleCors(this IServiceCollection services, IConfiguration configuration, ILogger logger)
        {
            var origins = configuration?.GetSection("Origins")?.Value?.Split(',').Where(o => !string.IsNullOrWhiteSpace(o)).ToArray();
            var allowAll = origins?.Length == 0 || origins!.Contains("*");

            logger.LogDebug("origins:{origins}", origins!);
            logger.LogDebug("allowAll:{allowAll}", allowAll);

            return services.AddCors(options =>
            {
                if (allowAll)
                {
                    options.AddPolicy("CorsPolicy", builder => builder
                        .AllowAnyOrigin()
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .WithExposedHeaders("Content-Disposition"));
                }
                else
                {
                    options.AddPolicy("CorsPolicy", builder => builder
                        .WithOrigins(origins!)
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .WithExposedHeaders("Content-Disposition"));
                }
            });
        }
    }
}
