using Microsoft.AspNetCore.Http.Features;
using S3.Core.Api.DependencyInjection;
using S3.Core.Api.Models;
using S3.Core.Application.Models;
using S3.Core.Infrastructures.DependencyInjection;
using S3.MoL.WelfareManagement.Api;
using S3.MoL.WelfareManagement.Api.Security;
using S3.MoL.WelfareManagement.Application.DependencyInjection;
using S3.MoL.WelfareManagement.Application.Models;
using S3.MoL.WelfareManagement.Application.Services.Persistence;
using S3.MoL.WelfareManagement.Infrastructure.DependencyInjection;

var builder = WebApplication.CreateBuilder(args);
ILogger logger = null!;

try
{
    var webApiAssembly = typeof(Program).Assembly;
    var appAssembly = typeof(IWelfareManagementDbContext).Assembly;

    //builder.AddConfiguration(webApiAssembly);

    builder.Services.Configure<FormOptions>(options => { options.ValueCountLimit = int.MaxValue; });

    builder.Services.AddMvc(options => { options.MaxModelBindingCollectionSize = int.MaxValue; });

    var welfareManagementConfiguration =
        WelfareManagementConfiguration.CreateFrom(builder.Configuration, webApiAssembly);
    builder.Services.AddSingleton(welfareManagementConfiguration);
    var webApiOptions = new WebApiOptions
    {
        Name = "WelfareManagement.Api",
        EnableControllers = true,
        EnableRequestLogging = true,
        EnableExceptionHandlingMiddleware = true,
        Configuration = builder.Configuration,
        SwaggerOptions = new SwaggerOptions
        {
            Enabled = true,
            Route = "WelfareManagement-api",
            IncludeXmlComments = true
        },
        RunAsWindowsService = welfareManagementConfiguration.RunAsWindowsService,
        WebApiAssembly = webApiAssembly,
        ApplicationAssembly = appAssembly,
        AddJwtAuthentication = true,
        AddAuthorization = true,
        AuthorizationOptions = Authorizations.Configure,
        DefaultLanguage = AppConstants.ArabicLanguage,
        AddCustomModelBinderProvider = true
    };

    logger = builder.AddWebApi(webApiOptions);
    builder.Services.AddApplication();
    builder.Services.AddInfrastructure(welfareManagementConfiguration,
        typeof(Program).Assembly);
    builder.Services.HandleCors(builder.Configuration, logger);

    var app = builder.Build();
    app.UseWebApi(webApiOptions);
    app.UseCors("CorsPolicy");
    app.Run();
}
catch (HostAbortedException exp)
{
    logger.LogDebug("HostAbortedException occurred, {details}", exp.Message);
}
catch (Exception exp)
{
    if (logger == null)
        Console.WriteLine($"Application failed with error: {exp.Message}");
    else
        logger.LogError(exp, "Application failed with error: {error}", exp.Message);
}
finally
{
    logger?.CloseAndFlush();
}