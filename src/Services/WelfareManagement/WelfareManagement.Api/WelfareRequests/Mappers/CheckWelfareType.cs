﻿using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Api.WelfareRequests.Dtos;
using S3.MoL.WelfareManagement.Application.WelfareRequests.Queries.CheckWelfareType;

namespace S3.MoL.WelfareManagement.Api.WelfareRequests.Mappers;

public static class CheckWelfareType
{
    internal static CheckWelfareTypeQuery CreateQuery(this (long LaborId, int WelfareTypeId) request)
    {
        return new CheckWelfareTypeQuery
        {
            LaborId = request.LaborId,
            WelfareTypeId = request.WelfareTypeId,
            Header = new RequestHeader
            {
                UserId = "system",
                UserName = "system",
                Roles = null,
                OrganizationUnitId = 1
            }
        };
    }

    internal static Result<CheckWelfareTypeResponseDto> CreateResponse(this Result<CheckWelfareTypeResult> result)
    {
        return new Result<CheckWelfareTypeResponseDto>
        {
            Value = result.Value != null
                ? new CheckWelfareTypeResponseDto
                {
                    IsBeneficairy = result.Value.IsBeneficairy
                }
                : null,
            Status = result.Status,
            ErrorMessage = result.ErrorMessage,
            Target = result.Target,
            Errors = result.Errors
        };
    }
}