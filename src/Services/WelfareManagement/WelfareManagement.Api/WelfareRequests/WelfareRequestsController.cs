﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using S3.Core.Application.Models.CQRS;
using S3.MoL.WelfareManagement.Api.WelfareRequests.Dtos;
using S3.MoL.WelfareManagement.Api.WelfareRequests.Mappers;

namespace S3.MoL.WelfareManagement.Api.WelfareRequests;


[Route("WelfareRequests")]
[ApiController]

public class WelfareRequestsController(IMediator mediator) : ControllerBase
{
    /// <summary>
    ///     Check eligibility for a welfare request based on LaborId and WelfareTypeId
    /// </summary>
    /// <param name="laborId">Worker's ID</param>
    /// <param name="welfareTypeId">Welfare Type ID</param>
    [HttpGet("{laborId:long}/{welfareTypeId:int}", Name = "CheckWelfareType")]
    public async Task<Result<CheckWelfareTypeResponseDto>> CheckWelfareType(
        [FromRoute] long laborId,
        [FromRoute] int welfareTypeId)
    {
        var query = (laborId, welfareTypeId).CreateQuery();

        var result = await mediator.Send(query);
        return result.CreateResponse();
    }
}