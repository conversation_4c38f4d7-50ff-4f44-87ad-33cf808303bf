﻿using Microsoft.AspNetCore.Authorization;
using S3.Core.Security.Extensions;

namespace S3.MoL.WelfareManagement.Api.Security
{
    internal static class Authorizations
    {

        public const string ReadConfig = "ReadConfig";
        public const string ManageConfig = "ManageConfig";

        public const string CheckWelfareType = "CheckWelfareType";

        public const string CreateWelfareRequest = "CreateWelfareRequest";
        public const string EditWelfareRequest = "EditWelfareRequest";
        public const string SearchWelfareRequest = "SearchWelfareRequest";

        public const string AddWorkflowAction = "AddWorkflowAction";

        public const string SearchLabors = "SearchLabors";
        //public const string ViewLabor = "ViewLabor";

        public const string SearchLaborWelfareRequests = "SearchLaborWelfareRequests";

        public static void Configure(AuthorizationOptions options)
        {
            options.AddPolicy(
              ReadConfig,
              policy =>
              {
                  policy.RequireAssertion(x =>
                      x.User.IsResearcher() ||
                      x.User.IsSystemAdmin() ||
                      x.User.HasCreateRequest()
                  );
              });

            options.AddPolicy(
              ManageConfig,
              policy =>
              {
                  policy.RequireAssertion(x =>
                      x.User.IsSystemAdmin()
                  );
              });

            options.AddPolicy(
                CheckWelfareType,
              policy =>
              {
                  policy.RequireAssertion(x =>
                      x.User.IsResearcher() ||
                      x.User.HasCreateWelfareRequest()
                  );
              });

            options.AddPolicy(
               CreateWelfareRequest,
               policy =>
               {
                   policy.RequireAssertion(x =>
                       x.User.IsResearcher() ||
                       x.User.HasCreateWelfareRequest()
                   );
               });

            options.AddPolicy(
              EditWelfareRequest,
              policy =>
              {
                  policy.RequireAssertion(x =>
                      x.User.IsResearcher() ||
                      x.User.HasEditWelfareRequest()
                  );
              });

            options.AddPolicy(
              AddWorkflowAction,
              policy =>
              {
                  policy.RequireAssertion(x =>
                      x.User.IsResearcher() ||
                      x.User.IsReviewer() ||
                      x.User.IsDirectorateManager()
                  );
              });

            options.AddPolicy(
               SearchWelfareRequest,
               policy =>
               {
                   policy.RequireAssertion(x =>
                       x.User.IsResearcher() ||
                       x.User.IsReviewer() ||
                       x.User.HasSearchWelfareRequest()
                   );
               });

            options.AddPolicy(
               SearchLabors,
               policy =>
               {
                   policy.RequireAssertion(x =>
                       x.User.IsResearcher() ||
                       x.User.HasCreateWelfareRequest()
                   );
               });


            options.AddPolicy(
                SearchLaborWelfareRequests,
             policy =>
             {
                 policy.RequireAssertion(x =>
                     x.User.IsResearcher()
                 );
             });
        }
    }
}
