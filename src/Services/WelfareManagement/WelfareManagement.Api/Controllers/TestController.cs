﻿using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace S3.MoL.WelfareManagement.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="CodesController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator for handling communication between components.</param>
        public TestController(IMediator mediator)
        {
            _mediator = mediator;
        }
        [HttpGet]
        public IActionResult Get()
        {
            return Ok("Test");
        }

    }
}
