﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>S3.MoL.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Core.Api" Version="0.0.70"/>
    </ItemGroup>
    <ItemGroup>
        <InternalsVisibleTo Include="WelfareManagement.IntegrationTest"/>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\WelfareManagement.Infrastructure\WelfareManagement.Infrastructure.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <None Remove="Properties\launchSettings.json"/>
    </ItemGroup>

</Project>
