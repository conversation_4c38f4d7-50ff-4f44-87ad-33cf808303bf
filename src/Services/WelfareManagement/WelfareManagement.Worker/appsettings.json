{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Elasticsearch", "Args": {"nodeUris": "http://**********:9200/", "indexFormat": "s3-mol.ils-{0:yyyy.MM}", "autoRegisterTemplate": true, "autoRegisterTemplateVersion": "ESv6"}}, {"Name": "File", "Args": {"path": "Logs\\.log", "rollingInterval": "Day"}}]}, "WelfareManagementConfiguration": {"RunAsWindowsService": false, "ConnectionString": "Server=**********;Database=MoL-WelfareManagement;User ID=mol-WelfareManagement;Password=********;TrustServerCertificate=True;", "CreateCareRequestSheetAtDay": 15, "Messaging": {"Host": {"HostUrl": "rabbitmq://**********:5672/", "VirtualHost": "/", "Username": "admin", "Password": "admin", "HeartbeatsInSeconds": 5}, "Consumers": [{"Name": "SyncTable8CodesConsumer", "Enabled": true, "QueueName": "WelfareManagement.SyncTable8Codes.Queue", "ConsumerFullTypeName": "S3.MoL.WelfareManagement.Worker.Consumers.SyncTable8CodesConsumer", "PrefetchCount": 100, "ConcurrentMessageLimit": 16, "RetryIntervalsInSeconds": [1, 1, 2, 2, 3], "BatchConfig": {"MessageLimit": 50, "TimeLimitInSeconds": 5, "ConcurrencyLimit": 1}}, {"Name": "SyncGenericCodesConsumer", "Enabled": true, "QueueName": "WelfareManagement.SyncGenericCodes.Queue", "ConsumerFullTypeName": "S3.MoL.WelfareManagement.Worker.Consumers.SyncGenericCodesConsumer", "PrefetchCount": 100, "ConcurrentMessageLimit": 16, "RetryIntervalsInSeconds": [1, 1, 2, 2, 3], "BatchConfig": {"MessageLimit": 50, "TimeLimitInSeconds": 5, "ConcurrencyLimit": 1}}, {"Name": "SyncWelfareManagementCodesConsumer", "Enabled": true, "QueueName": "WelfareManagement.WelfareManagementCodesChanged.Queue", "ConsumerFullTypeName": "S3.MoL.WelfareManagement.Worker.Consumers.SyncWelfareManagementCodesConsumer", "PrefetchCount": 100, "ConcurrentMessageLimit": 16, "RetryIntervalsInSeconds": [1, 1, 2, 2, 3], "BatchConfig": {"MessageLimit": 50, "TimeLimitInSeconds": 5, "ConcurrencyLimit": 1}}, {"Name": "SyncWorkFlowCodesConsumer", "Enabled": true, "QueueName": "WelfareManagement.WorkFlowCodesChanged.Queue", "ConsumerFullTypeName": "S3.MoL.WelfareManagement.Worker.Consumers.SyncWorkFlowCodesConsumer", "PrefetchCount": 100, "ConcurrentMessageLimit": 16, "RetryIntervalsInSeconds": [1, 1, 2, 2, 3], "BatchConfig": {"MessageLimit": 50, "TimeLimitInSeconds": 5, "ConcurrencyLimit": 1}}, {"Name": "SyncLaborsConsumer", "Enabled": true, "QueueName": "WelfareManagement.LaborsChanged.Queue", "ConsumerFullTypeName": "S3.MoL.WelfareManagement.Worker.Consumers.SyncLaborsConsumer", "PrefetchCount": 100, "ConcurrentMessageLimit": 16, "RetryIntervalsInSeconds": [1, 1, 2, 2, 3], "BatchConfig": {"MessageLimit": 50, "TimeLimitInSeconds": 5, "ConcurrencyLimit": 1}}, {"Name": "SyncPartiesConsumer", "Enabled": true, "QueueName": "WelfareManagement.PartiesChanged.Queue", "ConsumerFullTypeName": "S3.MoL.WelfareManagement.Worker.Consumers.SyncPartiesConsumer", "PrefetchCount": 100, "ConcurrentMessageLimit": 16, "RetryIntervalsInSeconds": [1, 1, 2, 2, 3], "BatchConfig": {"MessageLimit": 50, "TimeLimitInSeconds": 5, "ConcurrencyLimit": 1}}]}}}