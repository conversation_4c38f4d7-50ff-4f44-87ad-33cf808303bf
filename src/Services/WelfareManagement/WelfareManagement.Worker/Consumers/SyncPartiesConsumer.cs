﻿using MassTransit;
using MediatR;
using S3.Core.Worker.Models;
using S3.MoL.ContractManagement.Contracts.Messages;
using S3.MoL.WelfareManagement.Application.Parties.SyncParties;

namespace S3.MoL.WelfareManagement.Worker.Consumers
{
    public sealed class SyncPartiesConsumer : BaseConsumer<IPartyChanged>
    {
        private readonly IMediator _mediator;

        public SyncPartiesConsumer(IMediator mediator, ILogger<SyncPartiesConsumer> logger)
            : base(logger)
        {
            _mediator = mediator;
        }
        public override async Task Handle(ConsumeContext<IPartyChanged> context)
        {
            var command = MapCommand(context.Message);
            await _mediator.Send(command, context.CancellationToken);
        }

        private static SyncPartiesCommand MapCommand(IPartyChanged message)
        {
            return new SyncPartiesCommand()
            {
                PartyID = message.PartyID,
                Name = message.Name,
                PartyTypeID = (int)message.PartyTypeID
            };
        }
    }
}
