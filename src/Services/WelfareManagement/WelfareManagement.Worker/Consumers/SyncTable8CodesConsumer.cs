﻿using MassTransit;
using MediatR;
using S3.Core.Worker.Models;
using S3.MoL.Codes.Contracts.Messages;
using S3.MoL.Codes.Contracts.Models;

namespace S3.MoL.WelfareManagement.Worker.Consumers
{
    public sealed class SyncTable8CodesConsumer : BaseConsumer<ICodeChangedMessage>
    {
        private readonly IMediator _mediator;

        public SyncTable8CodesConsumer(IMediator mediator, ILogger<SyncTable8CodesConsumer> logger)
            : base(logger)
        {
            _mediator = mediator;
        }
        public override async Task Handle(ConsumeContext<ICodeChangedMessage> context)
        {
            var command = MapCommand(context.Message);
            await _mediator.Send(command, context.CancellationToken);
        }

        private static SyncTable8CodeCommand MapCommand(ICodeChangedMessage message)
        {
            return new SyncTable8CodeCommand()
            {
                ID = message.CodeId,
                Code = message.Value,
                CodeTypeId = message.CodeTypeId,
                IsActive = message.IsActive,
                IsDeleted = message.IsDeleted,
                ParentCodeId = message.ParentCodeID,
                Text = message.Text,
                Text2 = message.Text2!,
                WagesRate = MapMetaData(message.Metadata),
            };
        }

        private static decimal MapMetaData(IEnumerable<CodeField>? metadata)
        {
            var value = metadata?.FirstOrDefault(x => x.Key == "Rate")?.Value;
            return value == null ? 0 : decimal.Parse(value);
        }
    }
}
