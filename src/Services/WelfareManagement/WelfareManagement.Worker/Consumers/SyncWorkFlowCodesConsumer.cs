﻿using MassTransit;
using MediatR;
using S3.Core.Worker.Models;
using S3.MoL.Codes.Contracts.Messages;

namespace S3.MoL.WelfareManagement.Worker.Consumers
{
    public sealed class SyncWorkFlowCodesConsumer : BaseConsumer<ICodeChangedMessage>
    {
        private readonly IMediator _mediator;

        public SyncWorkFlowCodesConsumer(IMediator mediator, ILogger<SyncWorkFlowCodesConsumer> logger)
            : base(logger)
        {
            _mediator = mediator;
        }
        public override async Task Handle(ConsumeContext<ICodeChangedMessage> context)
        {
            var command = MapCommand(context.Message);
            await _mediator.Send(command, context.CancellationToken);
        }

        private static SyncWorkFlowCodesCommand MapCommand(ICodeChangedMessage message)
        {
            return new SyncWorkFlowCodesCommand()
            {
                ID = message.CodeId,
                Code = message.Value,
                CodeTypeId = message.CodeTypeId,
                IsActive = message.IsActive,
                IsDeleted = message.IsDeleted,
                ParentCodeId = message.ParentCodeID,
                Text = message.Text,
                Text2 = message.Text2,
            };
        }
    }
}
