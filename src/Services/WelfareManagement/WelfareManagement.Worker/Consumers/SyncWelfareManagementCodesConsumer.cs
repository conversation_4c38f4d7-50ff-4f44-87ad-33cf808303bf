﻿using MassTransit;
using MediatR;
using S3.Core.Worker.Models;
using S3.MoL.Codes.Contracts.Messages;
using S3.MoL.Codes.Contracts.Models;

namespace S3.MoL.WelfareManagement.Worker.Consumers
{
    public sealed class SyncWelfareManagementCodesConsumer : BaseConsumer<ICodeChangedMessage>
    {
        private readonly IMediator _mediator;

        public SyncWelfareManagementCodesConsumer(IMediator mediator, ILogger<SyncWelfareManagementCodesConsumer> logger)
            : base(logger)
        {
            _mediator = mediator;
        }
        public override async Task Handle(ConsumeContext<ICodeChangedMessage> context)
        {
            var command = MapCommand(context.Message);
            await _mediator.Send(command, context.CancellationToken);
        }

        private static SyncWelfareManagementCodesCommand MapCommand(ICodeChangedMessage message)
        {
            var amount = MapMetaData(message.Metadata, "Amount");
            return new SyncWelfareManagementCodesCommand()
            {
                ID = message.CodeId,
                Code = message.Value,
                CodeTypeId = message.CodeTypeId,
                IsActive = message.IsActive,
                IsDeleted = message.IsDeleted,
                ParentCodeId = message.ParentCodeID,
                Text = message.Text,
                Text2 = message.Text2,
                Amount = amount == null ? null : decimal.Parse(amount)
            };
        }
        private static string? MapMetaData(IEnumerable<CodeField>? metadata, string key)
        {
            return metadata?.FirstOrDefault(x => x.Key == key)?.Value;
        }
    }
}
