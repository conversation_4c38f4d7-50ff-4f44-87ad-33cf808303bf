﻿using MassTransit;
using MediatR;
using S3.Core.Worker.Models;
using S3.MoL.ContractManagement.Contracts.Messages;
using S3.MoL.WelfareManagement.Application.Labors.Commands.SyncLabors;
using LaborDto = S3.MoL.WelfareManagement.Application.Models.LaborDto;

namespace S3.MoL.WelfareManagement.Worker.Consumers
{
    public sealed class SyncLaborsConsumer : BaseConsumer<ILaborsChanged>
    {
        private readonly IMediator _mediator;
        public SyncLaborsConsumer(IMediator mediator, ILogger<SyncLaborsConsumer> logger)
            : base(logger)
        {
            _mediator = mediator;
        }

        public override async Task Handle(ConsumeContext<ILaborsChanged> context)
        {
            var command = MapCommand(context.Message);
            await _mediator.Send(command, context.CancellationToken);
        }

        private static SyncLaborCommand MapCommand(ILaborsChanged message)
        {
            return new SyncLaborCommand
            {
                Labors = message.Labors!.Select(x => new LaborDto
                {
                    CreatedByUserName = x.CreatedByUserName,
                    LaborID = x.LaborID,
                    BussinessNatureID = (int?)x.BussinessNatureID,
                    DirectorateID = x.DirectorateID,
                    PartyID = x.PartyID,
                    CreatedDate = x.CreatedDate,
                    Email = x.Email,
                    FullName = x.FullName,
                    MobileNo = x.MobileNo,
                    NationalID = x.NationalID,
                    OccupationId = x.Occupation!.ID,
                    UpdatedDate = x.UpdatedDate,
                    UpdatedUserName = x.UpdatedUserName,
                }).ToList()
            };
        }
    }
}
