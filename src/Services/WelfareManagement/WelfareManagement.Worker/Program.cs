﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using S3.Core.Infrastructures.DependencyInjection;
using S3.Core.Worker.DependencyInjection;
using S3.Core.Worker.Models;

var builder = Host.CreateApplicationBuilder();

ILogger logger = null!;
try
{
    var workerAssembly = typeof(Program).Assembly;

    builder.AddConfiguration(workerAssembly);

    var welfareManagementConfiguration = WelfareManagementConfiguration.CreateFrom(
        builder.Configuration,
        workerAssembly,
        "SyncGenericCodesConsumer",
        "SyncTable8CodesConsumer",
        "SyncWelfareManagementCodesConsumer",
        "SyncWorkFlowCodesConsumer",
        "SyncLaborsConsumer",
        "SyncPartiesConsumer");
    builder.Services.AddSingleton(welfareManagementConfiguration);

    var workerOptions = new WorkerOptions
    {
        Name = "WelfareManagement.Worker",
        Configuration = builder.Configuration,
        RunAsWindowsService = welfareManagementConfiguration.RunAsWindowsService
    };
    logger = builder.AddWorker(workerOptions);

    builder.Services.AddApplication();
    builder.Services.AddInfrastructure(
        welfareManagementConfiguration,
        workerAssembly);

    // Build the host
    //var host = builder.Build();

    // not to Start the Hangfire Dashboard web server
    var webHost = builder.Build();

    webHost.Start();

    // Run the worker host
    webHost.Run();
}
catch (HostAbortedException exp)
{
    logger.LogDebug("HostAbortedException occurred, {details}", exp.Message);
}
catch (Exception exp)
{
    if (logger == null)
    {
        Console.WriteLine($"Application failed with error: {exp.Message}");
    }
    else
    {
        logger.LogError(exp, "Application failed with error: {error}", exp.Message);
    }
}
finally
{
    logger?.CloseAndFlush();
}

// Method to create and configure the web host for the Hangfire Dashboard
//IWebHost CreateWebHost(IConfiguration configuration)
//{
//    return WebHost.CreateDefaultBuilder()
//        .ConfigureServices(services =>
//        {
//            services.AddCustomHangFire(configuration);
//        })
//        .Configure(app =>
//        {
//            app.UseRouting();
//            app.UseCustomHangFire();
//            app.UseHangFireJobs();
//        })
//        .UseUrls("http://localhost:5000") // Adjust the URL and port as needed
//        .Build();
//}
