﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
		<OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>S3.MoL.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
    <UserSecretsId>b8dd03c1-01c5-4052-8584-3423c1a376e9</UserSecretsId>
  </PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)'=='Release'">
		<DebugSymbols>False</DebugSymbols>
		<DebugType>None</DebugType>
		<SelfContained>false</SelfContained>
		<RuntimeIdentifier>win-x64</RuntimeIdentifier>
		<PublishSingleFile>true</PublishSingleFile>
	</PropertyGroup>

	<ItemGroup>
	  <None Remove="appsettings.json" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="appsettings.json" />
	</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Core.Application" Version="0.0.58" />
    <PackageReference Include="Core.Worker" Version="0.0.51" />
    <PackageReference Include="Codes.Contracts" Version="0.0.12" />
    <PackageReference Include="ContractManagement.Contracts" Version="0.0.36" />
    <PackageReference Include="Hangfire" Version="1.8.20" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
    <PackageReference Include="Hangfire.InMemory" Version="1.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WelfareManagement.Infrastructure\WelfareManagement.Infrastructure.csproj" />
  </ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.json">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>
</Project>
